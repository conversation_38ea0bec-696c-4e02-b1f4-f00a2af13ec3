const autoprefixer = require('autoprefixer');
const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin');
const createStyledComponentsTransformer = require('typescript-plugin-styled-components').default;
const styledComponentsTransformer = createStyledComponentsTransformer();
const paths = require('./config/paths');
const postcssFlexbugsFixes = require('postcss-flexbugs-fixes');

module.exports = function() {
  return {
    mode: 'development',
    stats: {
      errorDetails: true
    },
    devtool: 'eval',
    target: process.env.NODE_ENV === 'development' ? ['web', 'es5'] : 'browserslist',
    context: paths.appSrc,
    entry: [
      '@babel/polyfill',
      // 'webpack-hot-middleware/client',
      // 'react-hot-loader/patch',
      paths.appIndexJs,
      paths.appStyle,
      require.resolve('./src/styles/main.css'),
      path.join(paths.appSrc, 'index')
    ],
    output: {
      environment: {
        arrowFunction: false,
        bigIntLiteral: false,
        const: false,
        destructuring: false,
        dynamicImport: false,
        forOf: false,
        module: false
      },
      path: path.resolve(__dirname, 'build'),
      filename: 'js/bundle.js',
      publicPath: '/'
    },
    resolve: {
      modules: [path.join(__dirname, '/src'), 'node_modules'],
      extensions: ['.js', '.jsx', '.react.js', '.ts', '.tsx'],
      alias: {
        '@': paths.appSrc,
        images: path.resolve(__dirname, 'public/images'),
        fonts: path.resolve(__dirname, 'public/fonts'),
        vendys: `${paths.appSrc}/vendys-commons`,
        vcomponents: `${paths.appSrc}/vendys-commons/components`,
        styles: `${paths.appSrc}/vendys-commons/styles`,
        hooks: `${paths.appSrc}/vendys-commons/hooks`,
        context: `${paths.appSrc}/vendys-commons/context`,
        static: `${paths.appSrc}/vendys-commons/static`,
        utils: `${paths.appSrc}/vendys-commons/utils`,
        assets: `${paths.appSrc}/vendys-commons/assets`,
        rest: `${paths.appSrc}/vendys-commons/rest`,
        decorators: `${paths.appSrc}/vendys-commons/decorators`,
        state: `${paths.appSrc}/state`
      },
      fallback: {
        process: require.resolve('process/browser'),
        os: require.resolve('os-browserify/browser'),
        constants: require.resolve('constants-browserify')
      } // webpack 5 pollyfill
    },
    module: {
      rules: [
        {
          test: /\.html$/,
          loader: 'html-loader'
        },
        {
          test: /\.(woff|woff2|ttf|eot)$/,
          loader: 'file-loader',
          options: {
            name: 'fonts/[name].[ext]!static'
          },
          include: /node_modules/
        },
        {
          test: /\.svg$/,
          use: [
            {
              loader: '@svgr/webpack'
            }
          ]
        },
        {
          test: /\.(jpg|png|gif|otf|svg|webp|ttf|woff|woff2)(\?.*)?$/,
          use: [
            {
              loader: 'url-loader',
              options: { limit: 10000 }
            }
          ]
        },
        {
          test: /\.less/,
          use: [
            { loader: 'style-loader' },
            { loader: MiniCssExtractPlugin.loader },
            { loader: 'css-loader' },
            { loader: 'less-loader' }
          ]
        },
        {
          test: /.s?css$/,
          use: [
            'style-loader',
            MiniCssExtractPlugin.loader,
            'css-loader',
            {
              loader: 'postcss-loader',
              options: {
                postcssOptions: {
                  plugins: [
                    [
                      'postcss-preset-env',
                      {
                        // Options
                      }
                    ]
                  ]
                }
              }
            },
            'sass-loader'
          ]
        },
        {
          test: /\.ts(x?)$/, // Transform typescript files with ts-loader
          // exclude: /node_modules/,
          exclude: /node_modules\/(?!(axios|@redux-saga|redux-logger))/,
          use: [
            {
              loader: 'babel-loader',
              options: {
                plugins: [
                  [
                    'import',
                    {
                      libraryName: 'antd',
                      style: true
                    },
                    'antd-mobile'
                  ]
                ],
                babelrc: false,
                configFile: path.resolve(__dirname, 'babel.config.json'),
                compact: false,
                cacheDirectory: true,
                sourceMap: false
              }
            }, // using babel after typescript transpiles to target es6
            {
              loader: 'ts-loader',
              options: {
                transpileOnly: true, // fork-ts-checker-webpack-plugin is used for type checking
                logLevel: 'info',
                getCustomTransformers: () => ({
                  before: [styledComponentsTransformer]
                })
              }
            }
          ]
        },
        {
          test: /\.(js|jsx)$/,
          include: [paths.appSrc, /node_modules\/(react-spring|recoil)/],
          exclude: /node_modules\/(axios|@redux-saga|redux-logger)/,
          use: [
            // {
            //   loader: 'react-hot-loader'
            // },
            {
              loader: 'babel-loader',
              options: {
                babelrc: false,
                configFile: path.resolve(__dirname, 'babel.config.json'),
                compact: false,
                cacheDirectory: true,
                sourceMap: false,
                plugins: [
                  '@babel/plugin-transform-classes',
                  ['@babel/plugin-proposal-class-properties', { loose: true }],
                  'react-hot-loader/babel',
                  'transform-class-properties'
                  // require.resolve('react-refresh/babel')
                ]
              }
            }
          ]
        },
        {
          test: /\.m?js$/,
          resolve: {
            fullySpecified: false,
          },
        }
      ]
    },

    externals: {
      config: JSON.stringify(require('./config/config.api.host'))
    },

    plugins: [
      // infinite refresh 용으로
      // new webpack.DllReferencePlugin({
      //   context: path.join(__dirname),
      //   manifest: require('./build/vendor-manifest.json')
      // }),
      new webpack.ProvidePlugin({
        process: 'process/browser'
      }),
      new webpack.EnvironmentPlugin({
        NODE_ENV: 'development'
      }),
      new webpack.DefinePlugin({
        'process.env.NODE_ENV': '"development"',
        'process.env.DEBUG': JSON.stringify(process.env.DEBUG)
      }),
      new webpack.HotModuleReplacementPlugin(),
      // new ReactRefreshWebpackPlugin(),
      new HtmlWebpackPlugin({
        inject: true, //true 또는 body이면 모든 자바스크립트 리소스는 body 요소의 아래에 배치 됨
        template: paths.appHtml,
        favicon: paths.appFavicon
        // minify: {
        //         removeComments: true,
        //         collapseWhitespace: true,
        //         removeRedundantAttributes: true,
        //         useShortDoctype: true,
        //         removeEmptyAttributes: true,
        //         removeStyleLinkTypeAttributes: true,
        //         keepClosingSlash: true,
        //         minifyJS: true,
        //         minifyCSS: true,
        //         minifyURLs: true
        //     }
      }),
      // infinite refresh 용으로
      // new AddAssetHtmlPlugin({
      //   filepath: path.resolve(__dirname, './build/*.dll.js'),
      //   includeSourcemap: false // add this parameter
      // }),

      // new webpack.optimize.CommonsChunkPlugin({
      //     name: 'vendor',
      //     filename: 'js/vendor.js',
      // 		minChunks : 2
      // }),

      // new webpack.optimize.MinChunkSizePlugin({
      //   minChunkSize: 5120000 // 50kb
      // }),
      // default webpack upgrade
      // new webpack.optimize.OccurrenceOrderPlugin(),
      /*
			 UglifyJs2 : 사용 되지 않는 import문을 bundle.js file에 추가 되지 않게 설정

			 제거 된 import문은 콘솔창에 출력되게 옵션을 설정함.
			*/
      // new webpack.optimize.UglifyJsPlugin({
      //   compress: {
      //     unused: true // 콘솔 창에 출력 (false면 출력 안됨);
      //   },
      //   mangle: false, // DEMO ONLY: Don't change variable names.(난독화)
      //   beautify: true, // DEMO ONLY: Preserve whitespace (가독성 좋게 함)
      //   ouput: {
      //     comments: true // DEMO ONLY: Helpful comments (주석 삭제 안 함)
      //   }
      // }),
      new webpack.LoaderOptionsPlugin({
        minimize: true,
        debug: false
      }),
      new webpack.DefinePlugin({
        'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
        'process.env.ENV': JSON.stringify(process.env.NODE_ENV || 'development')
      }),
      new MiniCssExtractPlugin()
      // new ExtractTextPlugin('css/[name].[contenthash:8].css')
    ],
    optimization: {
      // minimizer: []
    },
    devServer: {
      hot: true,
      // historyApiFallback: {
      //   index: 'index.html'
      // },
      host: '0.0.0.0',
      historyApiFallback: true,
      disableHostCheck: true,
      port: 9000
    }
  };
};
process.noDeprecation = true;
