{"compilerOptions": {"target": "es6", "module": "esnext", "noImplicitAny": false, "preserveConstEnums": true, "baseUrl": "./src", "paths": {"@/*": ["./*"], "images/*": ["../public/images/*"], "fonts/*": ["../public/fonts/*"], "vcomponents/*": ["vendys-commons/components/*"], "containers/*": ["containers/*"], "vendys/*": ["vendys-commons/*"], "styles/*": ["vendys-commons/styles/*"], "hooks/*": ["vendys-commons/hooks/*"], "context/*": ["vendys-commons/context/*"], "static/*": ["vendys-commons/static/*"], "utils": ["vendys-commons/utils", "vendys-commons/utils/*"], "utils/*": ["vendys-commons/utils/*"], "assets/*": ["vendys-commons/assets/*"], "apis/*": ["apis/*"], "rest/*": ["vendys-commons/rest/*"], "decorators/*": ["vendys-commons/decorators/*"], "state/*": ["state/*"]}, "jsx": "react", "lib": ["dom", "es5", "es6", "es7", "es2017"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "declaration": false, "emitDecoratorMetadata": false, "experimentalDecorators": false, "forceConsistentCasingInFileNames": false, "moduleResolution": "node", "noEmitOnError": false, "noFallthroughCasesInSwitch": false, "noImplicitReturns": false, "noImplicitThis": false, "noUnusedLocals": false, "strict": false, "strictFunctionTypes": false, "pretty": false, "removeComments": false, "sourceMap": false, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true}, "include": ["src/**/*", "internals/templates/**/*"], "exclude": ["typings/browser.d.ts", "typings/browser", "node_modules/**/*"], "linterOptions": {"exclude": ["app/**", "components/**", "node_modules/**/*"]}}