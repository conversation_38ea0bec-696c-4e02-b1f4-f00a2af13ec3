## 식권대장 회사 관리자

### Prettier + eslint + airbnb 코드 스타일 자동 설정

> ##### VS code 설정
>
> ```
> 1. vs extention prettier 설치
> 2. npm install -g eslint
> ```
>
> ##### WebStorm 설정
>
> ```
> 1. plugin prettier 설치
> 2. npm install -g eslint
> 3. Preferences > Tools > File Watchers 추가 prettier
> -- 여기까지 했는데 안된다면
> 4. Preferences > Language & Frameworks > JavaScript
>       > Code Quality Tools > ESLint - ManualESLintConfiguration 선택, 자동 설정되면 저장
> ```

### container 규칙

> ##### container 생성 하는 기준
>
> ```
> LNB 메뉴별로 파일을 생성
> ex) 우리식대정책, 식대지급/내역, 식권신청 승인/내역
> ```
>
> ##### 파일 생성 naming 규칙
>
> ```
> 첫문자는 대문자로 시작하는 카멜표기법으로 한다
> LNB 네이밍으로 파일 생성한다.
> ex) 우리식대정책 -> CompanySikdaePolicy
> ```

### component 규칙

> #### component 생성 하는 기준
>
> ```
> container별 폴더를 생성 후 component를 나누는 기준에 따라 파일을 생성한다.
> component 나누는 기준은 개인의 생각대로 나눈다.(기준이 없음)
> ex)
>   1. CompanySikdaePolicy라는 container의 파일명으로 폴더를 생성
>   2. 폴더 안에 개인의 기준에 따라 component를 생성 (PolicyList.js, GropuList.js)
>   3. 각 component 폴더별 사용되는 component가 또 있을 시 해당 의미하는 이름으로 폴더를 생성 후 그 안에 파일을 만든다.
>   ex) component에서 Modal popup의 component를 생성 할 시  CompanySikdaePolicy폴더 안에 Modal 폴더를 생성 후 그 안에 파일을 생성한다.
> ```
>
> #### component naming 규칙
>
> ```
> 첫문자는 대문자로 시작하는 카멜표기법 한다
> 기본 생성 규칙은 없으며 대신 각각 의미하는 행동명을 파일명 뒤에 붙인다.
> 상세 : Dtl
> 리스트 : List
> 수정 : Mod
> 팝업 : Modal
> 추가 : Add
> ```
>
> #### 공통 component
>
> ```
> Commons폴더 안에 공통 component를 생성한다.
> ```

### function 생성 규칙

> ```
> 함수명 뒤에 각각 의미하는 행동명을 붙인다.
> 조회 : Sel
> 수정 : Mod
> 삭제 : Del
> 상세 : Dtl
> 리스트 : List
> 추가 : Add
> ```

### redux 규칙(action, service, reducers)

> ```
>
> ```

### 기타

> #### 공통
>
> ```
> 폴더명 및 파일은 commons로 생성한다.
> ```

### 코드 자동 생성

### ie11 지원

babel.config.js
module: entry 후에

index.js 에 삽입
import 'core-js/stable';
import 'regenerator-runtime/runtime';

## Resource

#### SVG

<SVGLOGO /> 형식의 컴포넌트 변환은 현재 사용하지 못함. build 방식이 url-loader 와 겹침

<img src={SVGLOGO} > 방식을 사용바람
