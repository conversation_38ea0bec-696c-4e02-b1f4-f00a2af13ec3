var path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
// const ExtractTextPlugin = require('extract-text-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

const paths = require('./config/paths');

module.exports = {
  mode: 'development',
  context: paths.appSrc,
  entry: ['@babel/polyfill', paths.appIndexJs, paths.appStyle],
  resolve: {
    modules: [path.join(__dirname, '/src'), 'node_modules'],
    alias: {
      images: path.resolve(__dirname, 'public/images'),
      fonts: path.resolve(__dirname, 'public/fonts')
    },
    fallback: { os: require.resolve('os-browserify/browser') } // webpack 5 pollyfill
  },

  module: {
    rules: [
      {
        test: /\.js$/,
        include: [paths.appSrc, /node_modules\/(react-spring|recoil)/],
        use: [
          {
            loader: 'babel-loader',
            options: {
              babelrc: false,
              configFile: path.resolve(__dirname, 'babel.config.json'),
              compact: false,
              cacheDirectory: true,
              sourceMap: false
              //   [
              //     'env',
              //     {
              //       targets: {
              //         browsers: ['> 1%', 'last 2 versions', 'not ie <= 9'],
              //         ie: '11'
              //       },
              //       module: false, // ES6 module 변환을 하지 않는다.
              //       useBuiltIns: 'entry', // babel-polyfill 사용
              //       debug: false
              //     }
              //   ],
              //   'react',
              //   'stage-2'
              // plugins: ['transform-class-properties', 'react-hot-loader/babel']
            }
          }
        ]
      },
      {
        test: /.s?css$/,
        use: [MiniCssExtractPlugin.loader, 'css-loader', 'sass-loader']
      },
      {
        test: /\.less/,
        use: [
          // compiles Less to CSS
          { loader: 'style-loader' },
          { loader: 'css-loader' },
          { loader: 'less-loader' }
        ]
      }
    ]
  },
  plugins: [
    new HtmlWebpackPlugin({
      inject: true, //true 또는 body이면 모든 자바스크립트 리소스는 body 요소의 아래에 배치 됨
      template: paths.appHtml,
      favicon: paths.appFavicon
    })
  ],
  optimization: {
    minimizer: []
  },
  devtool: 'cheap-module-source-map',
  devServer: {
    host: '127.0.0.1',
    contentBase: path.join(__dirname, '/'),
    compress: true,
    hot: true,
    inline: true,
    port: 9000,
    open: true
  }
};
