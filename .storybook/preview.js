import React from 'react';
import 'antd/dist/antd.css'; // or 'antd/dist/antd.less'
import { ThemeProvider, theme } from 'styles/themes';
import { IntlProvider } from 'react-intl';
import { ModalContextProvider } from 'context/ModalContext';
import LanguageProvider from 'vendys/translations/LanguageProvider';
import { GlobalStyle } from 'styles/global';

export const parameters = {
  actions: { argTypesRegex: '^on[A-Z].*' },
  controls: {
    matchers: {
      color: /(background|color)$/i,
      date: /Date$/
    }
  }
};

export const decorators = [
  (Story) => (
      <React.Suspense fallback={<div>...loading</div>}>
        <LanguageProvider>
          <ThemeProvider theme={theme['default']}>
            <ModalContextProvider>
              <GlobalStyle />
              <Story />
            </ModalContextProvider>
          </ThemeProvider>
        </LanguageProvider>
      </React.Suspense>
  )
];
