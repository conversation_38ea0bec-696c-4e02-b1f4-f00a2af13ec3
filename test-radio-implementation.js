// Test file to verify radio button implementation
// This file can be deleted after testing

const filterColumns = [
  {
    id: 'date',
    type: 'date-duration',
    span: 2.5,
    props: { sdate: 'moment().startOf("month")' }
  },
  {
    id: 'dateSearchType',
    type: 'radio',
    span: 1,
    data: {
      'REG': '신청일자로 검색',
      'EXECUTE': '지급/차감일자로 검색'
    },
    defaultValue: 'REG'
  },
  {
    id: 'code',
    type: 'rest-selector',
    props: {
      existTotal: true,
      totalText: '상태 전체',
      totalValue: '',
      defaultValue: ''
    },
    span: 0.5
  },
  {
    id: 'groupid',
    type: 'rest-selector',
    props: {
      existTotal: true,
      totalText: '복지그룹 전체',
      totalValue: '',
      defaultValue: ''
    },
    span: 0.5
  },
  { id: 'keyword', type: 'string', props: { placeholder: '사용자명, 신청자, 사유' }, span: 0.5 }
];

// Test the intiDefaultData calculation
const intiDefaultData = filterColumns.reduce((a, e) => ({ ...a, [e.id]: e.props?.defaultValue || e.defaultValue }), {});

console.log('Initial default data:', intiDefaultData);
console.log('Expected dateSearchType:', intiDefaultData.dateSearchType); // Should be 'REG'

// Test radio button data structure
const radioColumn = filterColumns.find(col => col.type === 'radio');
console.log('Radio column:', radioColumn);
console.log('Radio data keys:', Object.keys(radioColumn.data));
console.log('Radio data values:', Object.values(radioColumn.data));
