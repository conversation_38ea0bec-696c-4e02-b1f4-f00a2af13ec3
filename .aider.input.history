
# 2024-12-17 19:41:49.275897
+n

# 2024-12-17 19:43:23.215145
+/companion start

# 2024-12-17 19:44:33.408512
+타입 스크립트 코드를, 자바 스크립트로 변환할거야. IDE에 자동완성을 사용하게 CommonModel.ts에 User 를 CommonModel.js 에 정의해줘

# 2024-12-17 19:45:12.545308
+/map-refresh

# 2024-12-17 19:47:45.331334
+타입 스크립트 코드를, 자바 스크립트로 변환할거야. IDE에 자동완성을 사용하게 CommonModel.ts에 User 를 CommonModel.js 에 정의해줘

# 2024-12-17 19:49:33.344565
+JsDoc 말고, Object로 정의해줘

# 2024-12-17 19:50:55.744145
+/commit

# 2024-12-17 19:53:44.926853
+/map-refresh

# 2024-12-17 19:59:36.982823
+타입스크립트로 작성된 StatsList 를 KimAndChangStatsList.jsx 파일에 자바스크립트로 변환해줘

# 2024-12-17 20:01:56.910594
+/commit

# 2024-12-17 20:09:32.595431
+/map-refresh

# 2024-12-17 20:12:36.800136
+타입스크립트로 작성된 DateForm 에서 defaultDay 에 대한 로직 및 처리를 DateForm.js 에도 컨버팅 및 적용해줘

# 2024-12-17 20:15:30.779073
+/undo

# 2024-12-17 20:30:58.232678
+/clear

# 2024-12-17 20:31:00.224490
+/commit

# 2024-12-17 20:31:33.712449
+componentWillMount 에 defaultDay 에 대한 로직을 handleMonth 에도 적용해줘

# 2024-12-17 20:35:25.221662
+/commit

# 2024-12-18 12:28:50.624347
+n

# 2024-12-18 12:29:36.101976
+/companion start

# 2024-12-18 12:29:39.723886
+/map-refresh

# 2024-12-18 12:50:03.484832
+n

# 2024-12-18 12:50:28.637510
+/map-refresh

# 2024-12-18 12:50:40.975026
+/companion start

# 2024-12-18 12:50:44.650364
+/map

# 2024-12-18 12:51:25.725875
+/map-refresh

# 2024-12-18 12:51:31.526270
+/map

# 2024-12-18 12:52:49.255590
+/map-refresh

# 2024-12-18 12:52:55.574149
+/map

# 2024-12-18 12:54:48.160440
+n

# 2024-12-18 12:55:12.179194
+/map-refresh

# 2024-12-18 13:26:00.019319
+/companion start

# 2024-12-18 13:26:02.447109
+/map

# 2024-12-18 14:05:50.401207
+/map-refresh

# 2024-12-18 14:05:53.470179
+현재 DeductionSikdaeKimAndChang.jsx 에서 타입스크립트로 작성된 StatsList 를 사용하고 있어, 이걸 KimAndChangStatsList 를 사용하게 수정해

# 2024-12-18 14:44:46.939516
+/map-refresh

# 2024-12-18 14:48:52.294402
+DeductionSearchBox 에서 기존 동작과 코드는 그대로 유지 하면서, defaultDay만 옵션 프롭스값으로 입력받아서 DateForm 에 넘겨주게 수정해줘.

# 2024-12-18 14:51:37.026266
+/commit

# 2024-12-18 14:53:35.260459
+/map-refresh

# 2024-12-18 14:54:11.902098
+현재 DeductionSikdaeKimAndChang.jsx 에서 타입스크립트로 작성된 DeductionSearchBox 를 사용중이야, 이걸 DeductionSearchBox.js 사용하게 수정해줘

# 2024-12-18 17:08:28.673954
+/commit

# 2024-12-23 11:30:25.889879
+n

# 2024-12-23 11:30:46.053330
+/commit

# 2024-12-23 11:36:53.065663
+/companion start

# 2024-12-23 17:35:53.591887
+n

# 2024-12-23 17:40:48.903303
+/commit

# 2024-12-23 17:45:47.453150
+/companion start

# 2024-12-23 17:45:52.134108
+/map-refresh

# 2024-12-23 17:47:26.170232
+페이지 첫 진입시 API 호출을 안하고, 조회버튼을 눌렀을 때 호출하는 것으로 변경되었어.
+KimAndChangStatsList 에서, 데이터가 없어도 아래를 참고하여 디폴트로 테이블 헤더는 표시되게 수정해줘.
+
+{
+  "stat": [
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_99",
+      "name": "망곰_신인01",
+      "comIdNum": "",
+      "cellphone": "",
+      "totalPrice": 712418,
+      "companySupportPrice": 463072,
+      "salaryDeductionPrice": 249346,
+      "personalSupplyAmount": 226678,
+      "personalVatAmount": 22668
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_23",
+      "name": "망곰_강승호",
+      "comIdNum": null,
+      "cellphone": "01091088590",
+      "totalPrice": 207200,
+      "companySupportPrice": 134680,
+      "salaryDeductionPrice": 72520,
+      "personalSupplyAmount": 65927,
+      "personalVatAmount": 6593
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_32",
+      "name": "망곰_김재환",
+      "comIdNum": "BEARS32",
+      "cellphone": "01088888888",
+      "totalPrice": 63500,
+      "companySupportPrice": 41275,
+      "salaryDeductionPrice": 22225,
+      "personalSupplyAmount": 20204,
+      "personalVatAmount": 2021
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_25",
+      "name": "망곰_양의지",
+      "comIdNum": null,
+      "cellphone": null,
+      "totalPrice": 57000,
+      "companySupportPrice": 37050,
+      "salaryDeductionPrice": 19950,
+      "personalSupplyAmount": 18136,
+      "personalVatAmount": 1814
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_31",
+      "name": "망곰_정수빈",
+      "comIdNum": "",
+      "cellphone": null,
+      "totalPrice": 4500,
+      "companySupportPrice": 2925,
+      "salaryDeductionPrice": 1575,
+      "personalSupplyAmount": 1431,
+      "personalVatAmount": 144
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_103",
+      "name": "망곰_신인04",
+      "comIdNum": null,
+      "cellphone": null,
+      "totalPrice": 2500,
+      "companySupportPrice": 1625,
+      "salaryDeductionPrice": 875,
+      "personalSupplyAmount": 795,
+      "personalVatAmount": 80
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_104",
+      "name": "망곰_신인05",
+      "comIdNum": null,
+      "cellphone": null,
+      "totalPrice": 2500,
+      "companySupportPrice": 1625,
+      "salaryDeductionPrice": 875,
+      "personalSupplyAmount": 795,
+      "personalVatAmount": 80
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_105",
+      "name": "망곰_신인06",
+      "comIdNum": "",
+      "cellphone": "",
+      "totalPrice": 3000,
+      "companySupportPrice": 1950,
+      "salaryDeductionPrice": 1050,
+      "personalSupplyAmount": 954,
+      "personalVatAmount": 96
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_101",
+      "name": "망곰_신인02",
+      "comIdNum": null,
+      "cellphone": null,
+      "totalPrice": 2500,
+      "companySupportPrice": 1625,
+      "salaryDeductionPrice": 875,
+      "personalSupplyAmount": 795,
+      "personalVatAmount": 80
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_10000",
+      "name": "망곰_무제한식대",
+      "comIdNum": null,
+      "cellphone": null,
+      "totalPrice": 28400,
+      "companySupportPrice": 18460,
+      "salaryDeductionPrice": 9940,
+      "personalSupplyAmount": 9036,
+      "personalVatAmount": 904
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_jang",
+      "name": "망곰_장단위_관리자",
+      "comIdNum": null,
+      "cellphone": null,
+      "totalPrice": 36000,
+      "companySupportPrice": 23400,
+      "salaryDeductionPrice": 12600,
+      "personalSupplyAmount": 11454,
+      "personalVatAmount": 1146
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_77",
+      "name": "망곰_이승엽_감독",
+      "comIdNum": "",
+      "cellphone": "01091088598",
+      "totalPrice": 23100,
+      "companySupportPrice": 15015,
+      "salaryDeductionPrice": 8085,
+      "personalSupplyAmount": 7349,
+      "personalVatAmount": 736
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_1234",
+      "name": "망곰_모든정책_감독",
+      "comIdNum": null,
+      "cellphone": "01012341234",
+      "totalPrice": 140900,
+      "companySupportPrice": 91585,
+      "salaryDeductionPrice": 49315,
+      "personalSupplyAmount": 44831,
+      "personalVatAmount": 4484
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_4321",
+      "name": "망곰_모든정책_사원",
+      "comIdNum": null,
+      "cellphone": null,
+      "totalPrice": 58500,
+      "companySupportPrice": 38025,
+      "salaryDeductionPrice": 20475,
+      "personalSupplyAmount": 18613,
+      "personalVatAmount": 1862
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_jang_01",
+      "name": "망곰_장단위_사원",
+      "comIdNum": null,
+      "cellphone": null,
+      "totalPrice": 9000,
+      "companySupportPrice": 5850,
+      "salaryDeductionPrice": 3150,
+      "personalSupplyAmount": 2863,
+      "personalVatAmount": 287
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_1221",
+      "name": "망곰_모든정책_관리자",
+      "comIdNum": null,
+      "cellphone": null,
+      "totalPrice": 9000,
+      "companySupportPrice": 5850,
+      "salaryDeductionPrice": 3150,
+      "personalSupplyAmount": 2863,
+      "personalVatAmount": 287
+    }
+  ],
+  "total": {
+    "totalPrice": 1360018,
+    "companySupportPrice": 884012,
+    "salaryDeductionPrice": 476006,
+    "personalSupplyAmount": 432724,
+    "personalVatAmount": 43282
+  },
+  "paging": {
+    "page": 1,
+    "pagerow": 16,
+    "totalcount": 16
+  },
+  "table": [
+    {
+      "name": "날짜",
+      "property": "useDate"
+    },
+    {
+      "name": "ID",
+      "property": "signId"
+    },
+    {
+      "name": "이름",
+      "property": "name"
+    },
+    {
+      "name": "사번",
+      "property": "comIdNum"
+    },
+    {
+      "name": "휴대전화번호",
+      "property": "cellphone"
+    },
+    {
+      "name": "총사용식대",
+      "property": "totalPrice"
+    },
+    {
+      "name": "회사지원금액",
+      "property": "companySupportPrice"
+    },
+    {
+      "name": "개인사용금액",
+      "property": "salaryDeductionPrice"
+    },
+    {
+      "name": "개인사용공급가액",
+      "property": "personalSupplyAmount"
+    },
+    {
+      "name": "개인사용부가세",
+      "property": "personalVatAmount"
+    }
+  ]
+}

# 2024-12-23 17:50:56.340790
+페이지 초기 진입시, usageStat 을 초기화 해줘

# 2024-12-23 17:54:32.504355
+/map-refresh

# 2024-12-23 17:55:29.890093
+다른 페이지에서 설정된 usageStat 값이 김앤장 페이지로 넘어와서, 조회버튼을 눌러 검색 API를 호출하기 전 까지는 다른 페이지에서 설정된 값이 노출되어 버려

# 2024-12-23 17:57:09.443219
+bundle.js:91521 Uncaught ReferenceError: INITIALIZE_USAGE_STATS is not defined

# 2024-12-23 17:58:42.044396
+/map-refresh

# 2024-12-23 18:07:33.117783
+백엔드에선 조건내 데이터를 모두 내려주고, 프론트에서 페이징 처리 하는 것으로 변경되었어.
+아래 데이터를 참고하여 프론트 페이징처리되게 수정해
+
+{
+  "stat": [
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_99",
+      "name": "망곰_신인01",
+      "comIdNum": "",
+      "cellphone": "",
+      "totalPrice": 712418,
+      "companySupportPrice": 463072,
+      "salaryDeductionPrice": 249346,
+      "personalSupplyAmount": 226678,
+      "personalVatAmount": 22668
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_23",
+      "name": "망곰_강승호",
+      "comIdNum": null,
+      "cellphone": "01091088590",
+      "totalPrice": 207200,
+      "companySupportPrice": 134680,
+      "salaryDeductionPrice": 72520,
+      "personalSupplyAmount": 65927,
+      "personalVatAmount": 6593
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_32",
+      "name": "망곰_김재환",
+      "comIdNum": "BEARS32",
+      "cellphone": "01088888888",
+      "totalPrice": 63500,
+      "companySupportPrice": 41275,
+      "salaryDeductionPrice": 22225,
+      "personalSupplyAmount": 20204,
+      "personalVatAmount": 2021
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_25",
+      "name": "망곰_양의지",
+      "comIdNum": null,
+      "cellphone": null,
+      "totalPrice": 57000,
+      "companySupportPrice": 37050,
+      "salaryDeductionPrice": 19950,
+      "personalSupplyAmount": 18136,
+      "personalVatAmount": 1814
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_31",
+      "name": "망곰_정수빈",
+      "comIdNum": "",
+      "cellphone": null,
+      "totalPrice": 4500,
+      "companySupportPrice": 2925,
+      "salaryDeductionPrice": 1575,
+      "personalSupplyAmount": 1431,
+      "personalVatAmount": 144
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_103",
+      "name": "망곰_신인04",
+      "comIdNum": null,
+      "cellphone": null,
+      "totalPrice": 2500,
+      "companySupportPrice": 1625,
+      "salaryDeductionPrice": 875,
+      "personalSupplyAmount": 795,
+      "personalVatAmount": 80
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_104",
+      "name": "망곰_신인05",
+      "comIdNum": null,
+      "cellphone": null,
+      "totalPrice": 2500,
+      "companySupportPrice": 1625,
+      "salaryDeductionPrice": 875,
+      "personalSupplyAmount": 795,
+      "personalVatAmount": 80
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_105",
+      "name": "망곰_신인06",
+      "comIdNum": "",
+      "cellphone": "",
+      "totalPrice": 3000,
+      "companySupportPrice": 1950,
+      "salaryDeductionPrice": 1050,
+      "personalSupplyAmount": 954,
+      "personalVatAmount": 96
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_101",
+      "name": "망곰_신인02",
+      "comIdNum": null,
+      "cellphone": null,
+      "totalPrice": 2500,
+      "companySupportPrice": 1625,
+      "salaryDeductionPrice": 875,
+      "personalSupplyAmount": 795,
+      "personalVatAmount": 80
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_10000",
+      "name": "망곰_무제한식대",
+      "comIdNum": null,
+      "cellphone": null,
+      "totalPrice": 28400,
+      "companySupportPrice": 18460,
+      "salaryDeductionPrice": 9940,
+      "personalSupplyAmount": 9036,
+      "personalVatAmount": 904
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_jang",
+      "name": "망곰_장단위_관리자",
+      "comIdNum": null,
+      "cellphone": null,
+      "totalPrice": 36000,
+      "companySupportPrice": 23400,
+      "salaryDeductionPrice": 12600,
+      "personalSupplyAmount": 11454,
+      "personalVatAmount": 1146
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_77",
+      "name": "망곰_이승엽_감독",
+      "comIdNum": "",
+      "cellphone": "01091088598",
+      "totalPrice": 23100,
+      "companySupportPrice": 15015,
+      "salaryDeductionPrice": 8085,
+      "personalSupplyAmount": 7349,
+      "personalVatAmount": 736
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_1234",
+      "name": "망곰_모든정책_감독",
+      "comIdNum": null,
+      "cellphone": "01012341234",
+      "totalPrice": 140900,
+      "companySupportPrice": 91585,
+      "salaryDeductionPrice": 49315,
+      "personalSupplyAmount": 44831,
+      "personalVatAmount": 4484
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_4321",
+      "name": "망곰_모든정책_사원",
+      "comIdNum": null,
+      "cellphone": null,
+      "totalPrice": 58500,
+      "companySupportPrice": 38025,
+      "salaryDeductionPrice": 20475,
+      "personalSupplyAmount": 18613,
+      "personalVatAmount": 1862
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_jang_01",
+      "name": "망곰_장단위_사원",
+      "comIdNum": null,
+      "cellphone": null,
+      "totalPrice": 9000,
+      "companySupportPrice": 5850,
+      "salaryDeductionPrice": 3150,
+      "personalSupplyAmount": 2863,
+      "personalVatAmount": 287
+    },
+    {
+      "useDate": "2024-09-26~2024-10-25",
+      "startDate": {
+        "year": 2024,
+        "month": "SEPTEMBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 270,
+        "dayOfWeek": "THURSDAY",
+        "monthValue": 9,
+        "dayOfMonth": 26
+      },
+      "endDate": {
+        "year": 2024,
+        "month": "OCTOBER",
+        "leapYear": true,
+        "era": "CE",
+        "chronology": {
+          "calendarType": "iso8601",
+          "id": "ISO"
+        },
+        "dayOfYear": 299,
+        "dayOfWeek": "FRIDAY",
+        "monthValue": 10,
+        "dayOfMonth": 25
+      },
+      "signId": "bears_1221",
+      "name": "망곰_모든정책_관리자",
+      "comIdNum": null,
+      "cellphone": null,
+      "totalPrice": 9000,
+      "companySupportPrice": 5850,
+      "salaryDeductionPrice": 3150,
+      "personalSupplyAmount": 2863,
+      "personalVatAmount": 287
+    }
+  ],
+  "total": {
+    "totalPrice": 1360018,
+    "companySupportPrice": 884012,
+    "salaryDeductionPrice": 476006,
+    "personalSupplyAmount": 432724,
+    "personalVatAmount": 43282
+  },
+  "paging": {
+    "page": 1,
+    "pagerow": 16,
+    "totalcount": 16
+  },
+  "table": [
+    {
+      "name": "날짜",
+      "property": "useDate"
+    },
+    {
+      "name": "ID",
+      "property": "signId"
+    },
+    {
+      "name": "이름",
+      "property": "name"
+    },
+    {
+      "name": "사번",
+      "property": "comIdNum"
+    },
+    {
+      "name": "휴대전화번호",
+      "property": "cellphone"
+    },
+    {
+      "name": "총사용식대",
+      "property": "totalPrice"
+    },
+    {
+      "name": "회사지원금액",
+      "property": "companySupportPrice"
+    },
+    {
+      "name": "개인사용금액",
+      "property": "salaryDeductionPrice"
+    },
+    {
+      "name": "개인사용공급가액",
+      "property": "personalSupplyAmount"
+    },
+    {
+      "name": "개인사용부가세",
+      "property": "personalVatAmount"
+    }
+  ]
+}

# 2024-12-23 18:11:36.534441
+/map-refresh

# 2024-12-23 18:14:37.659396
+조회 API 호출 후, 응답이 400으로 리턴되는 경우 다음 보여줄 데이터를 참고하여 메세지를 모달창으로 출력해줘. 
+Request URL:
+https://dev-corp-api.mealc.co.kr/stat/v1/company/kim-and-chang/salary-deduction?startDate=August+1,+2024&endDate=October+25,+2024
+Request Method:
+GET
+Status Code:
+400 Bad Request
+Remote Address:
+**************:443
+Referrer Policy:
+strict-origin-when-cross-origin
+
+
+{
+  "status": 400,
+  "title": "에러",
+  "message": "검색 기간은 최대 60일까지 가능합니다"
+}

# 2024-12-23 18:14:40.446649
+s

# 2024-12-23 18:14:42.393094
+n

# 2024-12-23 18:18:09.524441
+/map-refresh

# 2024-12-23 18:18:20.822819
+코드를 더 제공했으니 다시 살펴봐줘

# 2024-12-23 18:19:27.791329
+/commit

# 2024-12-24 09:12:49.044542
+n

# 2024-12-24 09:14:28.891285
+/companion start

# 2024-12-24 09:15:06.733783
+현재 백엔드에서 모든 데이터를 내려주고, 프론트 페이징 처리 하고있어, 하지만 페이지 번호 클릭시 다시 조회 API를 호출하고 있어.

# 2024-12-24 09:35:38.686110
+/map-refresh

# 2024-12-24 09:37:09.008094
+DeductionSikdaeKimAndChang 에서만 시작날짜와 종료날짜를 62일 까지로 제한하고 싶어

# 2024-12-24 09:38:42.417611
+DeductionSearchBox 또한 공통 컴포넌트야,

# 2024-12-24 09:41:06.178493
+그걸 말하는게 아니라, DeductionSearchBox 를 이미 다른 페이지에서도 사용하고 있으니, 다른 페이지에서는 기존처럼 디폴트 92일 제한이 되어야 하고, DeductionSikdaeKimAndChang 에서만 62일 제한이 되어야 해

# 2024-12-24 09:41:43.447321
+/commit

# 2025-06-12 14:39:11.123941
+n

# 2025-06-12 14:51:37.513492
+/map-refresh

# 2025-06-12 18:58:26.900031
+n

# 2025-06-12 18:59:07.860359
+/paste

# 2025-06-12 18:59:13.444105
+/companion start

# 2025-06-12 18:59:37.312121
+/map-refresh

# 2025-06-12 19:00:21.826024
+백엔드 요청 파라미터에 아래와 같이 dateSearchType이 추가되었어.
+제공한 이미지처럼 dateSearchType 에 따른 라디오버튼과 enum값 전달 추가해줘.
+
+    @Data
+    public static class ListRequest {
+
+        private Date startdate;
+        private Date enddate;
+        private String code;
+        private Long groupid;
+        private Long policyid;
+        private String keyword;
+        private Integer page;
+        private Integer pagerow;
+        private DateSearchType dateSearchType;
+    }
+
+    public enum DateSearchType {
+        REGISTRATION_DATE("REG", "신청일자로 검색"),
+        EXECUTE_DATE("EXECUTE", "지급/차감일자로 검색");
+
+        private String code;
+        private String description;
+
+        DateSearchType(String code, String description) {
+            this.code = code;
+            this.description = description;
+        }
+
+        public String getCode() {
+            return code;
+        }
+
+        public String getDescription() {
+            return description;
+        }
+    }

# 2025-06-12 19:04:40.552258
+/drop ..\..\Users\skawn\AppData\Local\Temp\tmp_oqko0uw\clipboard_image.png

# 2025-06-12 19:04:45.557542
+/paste

# 2025-06-12 19:05:01.650293
+이미지와 같이 라디오박스에 텍스트가 보기 불편하게 세로로 출력되고 있어

# 2025-06-13 09:33:10.538597
+n

# 2025-06-13 09:37:10.151206
+/companion start

# 2025-06-13 09:38:14.357937
+/map-refresh

# 2025-06-13 09:38:44.768741
+n

# 2025-06-13 09:38:51.419539
+/companion start

# 2025-06-13 09:40:42.443908
+DateForm 컴포넌트에 maxDate 를 미래 선택이 가능한 옵션을 추가하고 싶어.
+DateForm은 여기저기서 공통 사용하는 컴포넌트이기에 기존과 동일하게 동작하며 SikdaeGivenForm 에서 사용할 때만 오늘 이후인 미래까지 날짜 선택이 가능하게 하고싶어.

# 2025-06-13 09:41:55.827877
+/commit

# 2025-06-13 11:12:40.813389
+n

# 2025-06-13 11:12:47.202233
+/map

# 2025-06-13 11:15:37.822379
+/companion start

# 2025-06-13 11:16:10.569477
+/map-refresh

# 2025-06-13 11:16:58.208703
+/paste

# 2025-06-13 11:22:25.750444
+/map-refresh

# 2025-06-13 11:23:21.615147
+검색 파라미터에 아래와 같이 dateSearchType 이 추가되었어.
+제공한 이미지를 확인하고 수정해줘.
+
+    @Data
+    public static class ListRequest {
+
+        private Date startdate;
+        private Date enddate;
+        private String code;
+        private Long groupid;
+        private Long policyid;
+        private String keyword;
+        private Integer page;
+        private Integer pagerow;
+        private DateSearchType dateSearchType;
+    }
+
+    @Getter
+    public enum DateSearchType {
+        REG("REG", "신청일자로 검색"),
+        EXECUTE("EXECUTE", "지급/차감일자로 검색");
+
+        private String code;
+        private String description;
+
+        DateSearchType(String code, String description) {
+            this.code = code;
+            this.description = description;
+        }
+    }

# 2025-06-13 11:25:53.129609
+제공한 이미지에 UI처럼 만들어줘. 현재는 dateSearchType이 드롭다운이야.

# 2025-06-13 11:32:35.088452
+이미지처럼 오른쪽에 상태, 그룹, 검색 등의 부분 영역을 아래줄로 내리고 싶어
