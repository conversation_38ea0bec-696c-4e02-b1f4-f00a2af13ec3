{"name": "company-web", "version": "v3.81.2", "description": "", "private": true, "scripts": {"postinstall": "node semantic-fix.js", "dev": "ENV=development node --inspect ./scripts/dev.js --hot", "start:dev": "cross-env ENV=development NODE_ENV=development webpack serve --mode=development", "start:prod": "ENV=production NODE_ENV=production webpack serve --mode=development", "test-build": "ENV=production-test node ./scripts/build.js", "staging-build": "ENV=production-stage node ./scripts/build.js", "build": "ENV=production NODE_ENV=production node ./scripts/build.js", "start": "react-scripts start", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook", "make": "plop --plopfile internals/generators/index.js"}, "author": "", "license": "ISC", "dependencies": {"@ant-design/icons": "^4.7.0", "@babel/cli": "^7.14.8", "@babel/core": "^7.14.8", "@babel/plugin-proposal-class-properties": "^7.14.5", "@babel/plugin-proposal-decorators": "^7.14.5", "@babel/plugin-proposal-export-default-from": "^7.14.5", "@babel/plugin-proposal-object-rest-spread": "^7.14.7", "@babel/plugin-proposal-private-methods": "^7.14.5", "@babel/plugin-proposal-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-export-default-from": "^7.14.5", "@babel/plugin-transform-arrow-functions": "^7.14.5", "@babel/plugin-transform-modules-commonjs": "^7.14.5", "@babel/plugin-transform-regenerator": "^7.14.5", "@babel/plugin-transform-runtime": "^7.14.5", "@babel/polyfill": "^7.12.1", "@datadog/browser-rum": "^4.42.2", "@hot-loader/react-dom": "^17.0.1", "@storybook/manager-webpack5": "^6.3.2", "@svgr/webpack": "^5.5.0", "@types/react-window": "^1.8.5", "abortcontroller-polyfill": "^1.7.3", "antd": "^4.16.3", "antd-mobile": "^2.3.4", "autobind-decorator": "^1.4.0", "autoprefixer": "^8.2.0", "axios": "^0.16.1", "babel-core": "^7.0.0-bridge.0", "babel-plugin-styled-components": "^1.13.2", "canvg": "^4.0.2", "case-sensitive-paths-webpack-plugin": "^2.4.0", "chart.js": "^3.3.2", "chartjs-plugin-annotation": "^1.0.2", "classnames": "^2.2.5", "constants-browserify": "^1.0.0", "core-js": "^3.15.2", "cron": "^1.3.0", "css-loader": "^5.2.2", "downloadjs": "^1.4.7", "exceljs": "^4.4.0", "faker": "^5.5.3", "file-saver": "^2.0.2", "history": "4.10.1", "html-loader": "^2.1.2", "html-webpack-plugin": "^5.3.1", "html2canvas": "^1.0.0-alpha.12", "jsoneditor": "^9.4.0", "jspdf": "^2.5.1", "less-plugin-autoprefix": "^1.5.1", "mini-css-extract-plugin": "^1.4.1", "moment": "^2.18.1", "moment-timezone": "^0.5.17", "numeral": "^2.0.6", "os-browserify": "^0.3.0", "plop": "^2.7.4", "postcss-flexbugs-fixes": "^3.3.0", "postcss-loader": "^5.2.0", "postcss-preset-env": "^6.7.0", "process": "^0.11.10", "promise-polyfill": "^8.2.0", "prop-types": "^15.5.8", "query-string": "^5.1.1", "ramda": "^0.26.1", "raw-loader": "^4.0.2", "rc-time-picker": "^2.4.0", "react": "^17.0.2", "react-addons-update": "^15.5.2", "react-animate-on-scroll": "^2.1.5", "react-animated-css": "^1.2.1", "react-app-polyfill": "^2.0.0", "react-barcode": "^1.4.0", "react-chartjs-2": "^3.0.3", "react-currency-format": "^1.0.0", "react-datepicker": "^0.46.0", "react-dom": "^17.0.2", "react-ga4": "^2.1.0", "react-infinite": "^0.11.0", "react-infinite-scroll-component": "^4.5.2", "react-intl": "^5.20.2", "react-lottie": "^1.2.3", "react-motion": "^0.5.2", "react-number-format": "^4.6.3", "react-query": "^3.18.1", "react-redux": "^5.0.4", "react-router-dom": "4.1.1", "react-router-dom-new": "npm:react-router-dom@^5.2.0", "react-show": "^3.0.4", "react-sortable-hoc": "^0.6.3", "react-spring": "^9.2.4", "react-swipeable-tab": "^0.1.1", "react-swipeable-views": "^0.14.0", "react-table": "^6.0.5", "react-table-new": "npm:react-table@^7.7.0", "react-to-pdf": "0.0.8", "react-toggle-display": "^2.2.0", "react-tooltip": "^3.3.0", "react-transition-group": "^4.4.2", "react-use": "^17.2.4", "react-virtualized-auto-sizer": "^1.0.6", "react-visibility-sensor": "^5.1.1", "react-window": "^1.8.6", "recharts": "^1.0.0-alpha.2", "recoil": "^0.3.1", "redux": "^3.6.0", "redux-actions": "^2.0.2", "redux-form": "^7.0.3", "redux-injectors": "^1.3.0", "redux-logger": "^3.0.1", "redux-promise-middleware": "^4.2.0", "redux-thunk": "^2.2.0", "regenerator-runtime": "^0.13.9", "sass": "^1.32.10", "sass-loader": "^11.0.1", "semantic-ui-less": "^2.4.1", "semantic-ui-react": "^0.88.2", "styled-components": "^5.3.0", "styled-media-query": "^2.1.2", "svg-inline-loader": "^0.8.2", "ts-loader": "^9.2.3", "typescript-plugin-styled-components": "^1.5.0", "use-http": "^1.0.21", "webpack": "^5.33.2", "webpack-cli": "^4.6.0", "webpack-dev-server": "^3.11.2", "webpack-hot-middleware": "^2.25.0", "xlsx": "^0.16.2"}, "devDependencies": {"@pmmmwh/react-refresh-webpack-plugin": "^0.4.3", "@storybook/addon-actions": "^6.2.9", "@storybook/addon-essentials": "^6.2.9", "@storybook/addon-links": "^6.2.9", "@storybook/builder-webpack5": "^6.2.9", "@storybook/react": "^6.2.9", "@types/react-router-dom": "^5.1.7", "@typescript-eslint/eslint-plugin": "^4.28.0", "@typescript-eslint/parser": "^4.28.0", "babel-eslint": "^7.2.1", "babel-loader": "7.1.2", "babel-plugin-import": "^1.13.3", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-runtime": "^6.23.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.1", "babel-preset-react-app": "^3.1.0", "babel-preset-stage-2": "^6.24.1", "cross-env": "^7.0.3", "chalk": "^1.1.3", "detect-port": "^1.2.1", "eslint": "^6.6.0", "eslint-config-airbnb": "^18.0.1", "eslint-config-prettier": "^6.10.0", "eslint-config-react-app": "^5.2.0", "eslint-import-resolver-typescript": "^2.5.0", "eslint-loader": "^2.0.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-prettier": "^3.1.2", "eslint-plugin-react": "^7.16.0", "eslint-plugin-react-hooks": "^1.7.0", "file-loader": "^6.2.0", "filesize": "^3.5.6", "gzip-size": "^3.0.0", "less": "^2.7.2", "less-loader": "^4.0.5", "prettier": "^1.19.1", "prettier-eslint": "^9.0.1", "react-hot-loader": "^4.13.0", "react-refresh": "^0.10.0", "readline": "^1.3.0", "recursive-readdir": "^2.1.1", "rimraf": "^2.6.1", "strip-ansi": "^3.0.1", "style-loader": "^0.18.2", "url-loader": "^4.1.1", "webpack-merge": "^4.1.0"}, "browserslist": {"production": ["ie 11", ">0.2%", "not dead", "not op_mini all"], "development": ["ie 11", "last 1 version", "> 1%", "maintained node versions", "not dead"]}, "browser": {"crypto": false, "fs": false}}