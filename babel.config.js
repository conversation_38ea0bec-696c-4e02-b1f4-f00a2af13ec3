// babel.config.js
module.exports = function(api) {
  if (api) api.cache(true);
  // api.babelrc(false);
  const presets = [
    [
      '@babel/preset-env',
      {
        corejs: {
          version: '3',
          proposals: true
        },
        targets: {
          browsers: ['edge >= 16', 'safari >= 9', 'firefox >= 57', 'ie >= 11', 'ios >= 9', 'chrome >= 49'],
          edge: '17',
          firefox: '60',
          chrome: '67',
          safari: '11.1',
          ie: '11',
          esmodules: true
        },
        modules: 'cjs',
        useBuiltIns: 'entry',
        corejs: 3,
        debug: true,
        shippedProposals: true
      }
    ],
    '@babel/preset-react'
  ];
  const plugins = [
    [
      '@babel/plugin-proposal-class-properties',
      {
        loose: true
      }
    ],
    [
      '@babel/plugin-transform-classes',
      {
        loose: true
      }
    ],
    [
      '@babel/plugin-transform-arrow-functions',
      {
        spec: true
      }
    ],
    [
      '@babel/plugin-syntax-dynamic-import',
      {
        legacy: true
      }
    ],
    // '@babel/plugin-transform-runtime',
    [
      '@babel/plugin-proposal-decorators',
      {
        // decoratorsBeforeExport: true,
        legacy: true
      }
    ],
    [
      '@babel/plugin-proposal-private-methods',
      {
        loose: true
      }
    ],
    [
      '@babel/plugin-proposal-private-property-in-object',
      {
        loose: true
      }
    ],
    '@babel/plugin-proposal-object-rest-spread',

    [
      '@babel/plugin-transform-regenerator',
      {
        asyncGenerators: false,
        generators: false,
        async: false
      }
    ],
    [
      '@babel/plugin-transform-runtime',
      {
        absoluteRuntime: false,
        corejs: 3,
        helpers: false,
        regenerator: true,
        version: '7.0.0-beta.0'
      }
    ],
    [
      '@babel/plugin-transform-modules-commonjs',
      {
        allowTopLevelThis: true
      }
    ],
    'babel-plugin-styled-components'
  ];

  return {
    presets,
    plugins
  };
};
