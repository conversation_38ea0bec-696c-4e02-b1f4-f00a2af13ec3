process.env.ENV = 'development';

const detect = require('detect-port');
const webpack = require('webpack');
const WebpackDevServer = require('webpack-dev-server');
const chalk = require('chalk');

const config = require('../config/webpack.dev.config');
const prompt = require('../config/prompt');

const DEFAULT_PORT = process.env.PORT || 3000;
const isSomkeText = process.argv.some(arg => arg.indexOf("--smoke-test") > -1);
var compiler;
var handleCompile;

if(isSomkeText) {

	handleCompile = function(err, stats) {
		console.log(err, stats);
		//error 발생 시 프로세스 종료 후 1을 리턴
		if(err || stats.hasErrors() || stats.hasWarnings()) {
			process.exit(1);

		//error가 없을 시 프로세스 종료 후 0을 리턴
		}else {
			process.exit(0);
		}
	}

};

// Webpack 출력을 나타내는 유틸
var friendlySyntaxErrorLabel = 'Syntax error:';

function isLikelyASyntaxError(message) {
	return message.indexOf(friendlySyntaxErrorLabel) !== -1;
}


// message format
function formatMessage(message) {
	return message
		// Make some common errors shorter:
		.replace(
			// Babel syntax error
			'Module build failed: SyntaxError:',
			friendlySyntaxErrorLabel
		)
		.replace(
			// Webpack file not found error
			/Module not found: Error: Cannot resolve 'file' or 'directory'/,
			'Module not found:'
		)
		// Internal stacks are generally useless so we strip them
		.replace(/^\s*at\s.*:\d+:\d+[\s\)]*\n/gm, '') // at ... ...:x:y
		// Webpack loader names obscure CSS filenames
		.replace('./~/css-loader!./~/postcss-loader!', '');
}

// 가장 최근 빌드에 집중할 수 있도록 출력을 지우려는 의도입니다.
function clearConsole() {
	process.stdout.write('\x1bc');
}


function setupCompiler(port) {

	compiler = webpack(config, handleCompile);

	compiler.hooks.invalid.tap(
		'*', function(){
			clearConsole();
			console.log("### Compiling...");
	
		});
	  

	// compiler.plugin("invalid", function() {

	// 	clearConsole();
	// 	console.log("### Compiling...");

	// });

	// //컴파일 성공 후
	// compiler.plugin("done", function(stats) {

	// 	clearConsole();
	// 	const hasErrors = stats.hasErrors();
	// 	const hasWarnings = stats.hasWarnings();

	// 	if(!hasErrors && !hasWarnings) {

	// 		console.log(chalk.green('##### Compiled successfully!'))
	// 		console.log();
	// 		console.log("##### hasErrors 	: ", hasErrors);
	// 		console.log("##### hasWarnings 	: ", hasWarnings);
	// 		console.log("##### The app is running at : company-web");
	// 		console.log("##### URL : " + chalk.cyan("http://localhost:" + port + "/"));
	// 		console.log();
	// 		return;

	// 	}

	// 	const json = stats.toJson({}, true);		//컴파일 정보를 JSON 객체로 반환한다.
	// 	let formattendError = json.errors.map(message => "Error in " + formatMessage(message));
	// 	const formattedWarnings = json.warnings.map(message => 'Warning in ' + formatMessage(message));

	// 	if(hasErrors) {

	// 		console.log(chalk.red("##### Filed to compile..."));
	// 		console.log();

	// 		// Syntax error가 포함 된 에러만 추출.
	// 		if(formattendError.some(isLikelyASyntaxError)) {
	// 			formattendError = formattendError.filter(isLikelyASyntaxError);
	// 		}
	// 		formattendError.forEach(message => {
	// 			console.log("##### " + message);
	// 			console.log();
	// 		});

	// 		return;

	// 	}

	// 	if(hasWarnings) {

	// 		console.log(chalk.yellow("##### Compiled with warnings."));
	// 		console.log();

	// 		formattedWarnings.forEach(message => {
	// 			console.log(message);
	// 			console.log();
	// 		});

	// 	}

	// });

}

// 'proxy'는 개발하는 동안 대체 서버를 지정 할 수 있게 한다.
function addMiddleware(devServer) {
	var proxy = require(paths.appPackageJson).proxy;

}

function runDevServer(port) {

	const devServer = new WebpackDevServer(compiler, {

		hot : true,
		historyApiFallback : true,
		contentBase: config.output.publicPath,
		publicPath : config.output.publicPath,
		quiet : true,
		watchOptions : {
			ignored : /node_modules/
		},
		disableHostCheck : true

	});

	devServer.listen(port, (err, result) => {
		console.log("#####  찍혀야지")
		if(err) {
			return console.log(err);
		}
		console.log(err)
		console.log(result);
		// clearConsole();
		console.log(chalk.cyan("##### Starting the development server ..."));
		console.log();

	});

}

function run(port) {

	setupCompiler(port);
	runDevServer(port);

}

detect(DEFAULT_PORT).then(port => {

	console.log("#### port : ", port);

	if(port === DEFAULT_PORT) {
		run(port);
		return;
	}

	// clearConsole();

	var question =
		chalk.yellow("Something is already running on port " + DEFAULT_PORT + ".") +
		chalk.yellow("\n이미 3000 port가 활성화 되어 있구먼~ 다른 port로 실행 할겨??");

	prompt(question, true).then(shouldChangePort => {
		if (shouldChangePort) {
			run(port);
		}
	});

})
