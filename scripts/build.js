/*
#####

	filesize : 숫자 또는 문자열을 파일 크기을 가져오는 간단한 방법을 제공
	strip-ansi : ANSI escape codes 변환

#####
*/
const chalk = require('chalk');
const fs = require('fs');
const path = require('path');
const webpack = require('webpack');

const recursive = require('recursive-readdir');
const gzipSize = require('gzip-size').sync;
const rimrafSync = require('rimraf').sync;
const filesize = require('filesize');
const stripAnsi = require('strip-ansi');


const config = require('../config/webpack.prod.config');
const paths = require('../config/paths');


/*

	build의 css 및 js 파일 위치의 경로를 return한다.
	Input: /User/dan/app/build/static/js/main.82be8.js
	Output: /static/js/main.js

*/
const removeFileNameHash = (fileName) => {

	return fileName
		.replace(paths.appBuild, '')
		.replace(/\/?(.*)(\.\w+)(\.js|\.css)/, (match, p1, p2, p3) => p1 + p3);

}

/*
	현재 파일과 이전 파일 사이즈 비교한 파일 사이즈 리턴
	Input: 1024, 2048
	Output: "(+1 KB)"
*/
const getDifferenceLabel = (currentSize, previousSize) => {

	const FIFTY_KILOBYTES = 1024 *50;
	let difference = currentSize - previousSize;
	let fileSize = !Number.isNaN(difference) ? filesize(difference) : 0;

	if(difference >= FIFTY_KILOBYTES) {
		return chalk.red("+" + fileSize);
	}else if(difference < FIFTY_KILOBYTES && difference > 0) {
		return chalk.yellow("+" + fileSize);
	}else if(difference < 0) {
		return chalk.green(fileSize);
	}else {
		return "";
	}

}


/*
	Print a detailed summary of build files.
	이전 파일과 현제 build 파일 비교 출력
*/
const printFileSize = (stats, previousSizeMap) => {

	var assets = stats.toJson().assets
		.filter(asset => /\.(js|css)$/.test(asset.name))
		.map(asset => {

			let fileContents = fs.readFileSync(paths.appBuild + "/" + asset.name);
			let size = gzipSize(fileContents);
			let previousSize = previousSizeMap[removeFileNameHash(asset.name)];
			let difference = getDifferenceLabel(size, previousSize);

			return {

				folder : path.join("build", path.dirname(asset.name)),
				name : path.basename(asset.name),
				size : size,
				sizeLabel : filesize(size) + (difference ? " (" + difference + ") " : "")

			};

		});

	assets.sort((a,b) => b.size - a.size);
	const longsetSizeLabelLength = Math.max.apply(null,
		assets.map(a => stripAnsi(a.sizeLabel).length)
	);

	assets.forEach(asset => {

		let sizeLabel = asset.sizeLabel;
		let sizeLength = stripAnsi(sizeLabel).length;

		if(sizeLength < longsetSizeLabelLength) {

			let rightPadding = "  ".repeat(longsetSizeLabelLength - sizeLength);
			sizeLabel += rightPadding;

		}

		console.log(
			"  " + sizeLabel +
			"  " + chalk.dim(asset.folder + path.sep) + chalk.cyan(asset.name)
		)

	});

}

//build 함수
const build = (previousSizeMap) => {

	console.log(chalk.blue("Creating an optimized production build..."));
	webpack(config).run((err, stats) => {

		if(err) {

			console.error(chalk.red("Failed to create a production build. Reason : "));
			console.error(err.message || err);
			process.exit(1);

		}

		console.log(chalk.green("Compiled successfully."));
		console.log();

		console.log("File sizes after gzip : ");
		console.log();
		printFileSize(stats, previousSizeMap);
		console.log();

	});

}

/*
	모듈 : recursive는 디렉토리 및 하위 디렉토리의 모든 파일을 나열한다.
	용도 : 파일의 사이즈를 체크 변경 된 파일을 나열 한다.
*/
recursive(paths.appBuild, (err, fileNames) => {

	//이전 사이즈
	var previousSizeMap = (fileNames || [])
		.filter(fileName => /\.(js|css)$/.test(fileName))
		.reduce((memo, fileName) => {

			var contents = fs.readFileSync(fileName);
			var key = removeFileNameHash(fileName);

			/*
				Get the gizpped size of a string or bufiier
				file 사이지 반환
			*/
			memo[key] = gzipSize(contents);

			return memo;

		}, {});

	// build folder의 모든 파일을 삭제 한다.
	rimrafSync(paths.appBuild + "/*");

	// build start
	build(previousSizeMap);
});
