/**
*
* {{ properCase name }}
*
*/
{{#if memo}}
import React, { memo } from 'react';
{{else}}
import React from 'react';
{{/if}}

import { Container } from './styles'

// import styled from 'styles/styled-components';

{{#if wantMessages}}
import { FormattedMessage } from 'react-intl';
import messages from './messages';
{{/if}}

interface {{ properCase name }}Props {}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
function {{ properCase name }}(props: {{ properCase name }}Props) {
  return (
  <Container>
    {{#if wantMessages}}
    <FormattedMessage {...messages.header} />
    {{/if}}
  </Container>
  );
};

{{#if memo}}
export default memo({{ properCase name }});
{{else}}
export default {{ properCase name }};
{{/if}}