import React, { ReactElement, useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { useQuery } from 'react-query';

import api from 'config';
import { FormattedMessage } from 'react-intl';

import messages from './messages';
import { columns } from './viewMeta';
import { Container } from './styles';
import { defaultAction } from './actions';

interface {{ properCase name }}Props {

}

function {{ properCase name }}({}: {{ properCase name }}Props): ReactElement {
  const [todos, setTodos] = useState();
  const { isLoading, isError, data: response = {} } = useQuery(`apiKey`, () => ());

  useEffect(() => {
    initialData()
  }, [])

  async function initialData() {
    const initialTodos = await get('/stat/v1/company/division/top')
    if (response.ok) {
      var data = await response.json()
      setTodos(data)
    }
  }

  return (
    <Container>
      <div><FormattedMessage {...messages.header} /></div>
      {todos && JSON.stringify(todos)}
    </Container>
  )
}

const mapStateToProps = (state) => {
  // return { todos: state.todos };
  return {

  };
}

const mapDispatchToProps = (dispatch, ownProps) => {
  // const boundActions = bindActionCreators({ defaultAction }, dispatch)
  return {
    // dispatchPlainObject: () => dispatch({ type: 'MY_ACTION' }),
    // dispatchActionCreatedByActionCreator: () => dispatch(createMyAction()),
    // ...boundActions,
  }
}

export default connect(mapStateToProps, mapDispatchToProps)({{ properCase name }})
