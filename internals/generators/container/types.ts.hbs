import { ActionType } from 'typesafe-actions';
{{#if wantActionsAndReducer}}
import * as actions from './actions';
{{/if}}

/* --- STATE --- */
interface {{ properCase name }}State {
  readonly default: any;
}


/* --- ACTIONS --- */
{{#if wantActionsAndReducer}}
type {{ properCase name }}Actions = ActionType<typeof actions>;
{{/if}}

/* --- EXPORTS --- */
type ContainerState = {{ properCase name }}State;
{{#if wantActionsAndReducer}}
type ContainerActions = {{ properCase name }}Actions;
{{/if}}

{{#if wantActionsAndReducer}}
export { ContainerState, ContainerActions };
{{else}}
export { ContainerState };
{{/if}}
