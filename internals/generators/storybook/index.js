/**
 * Component Generator
 */

/* eslint strict: ["off"] */

'use strict';

const componentExists = require('../utils/componentExists');

module.exports = {
  description: 'Add an component storybook',
  prompts: [
    {
      type: 'input',
      name: 'name',
      message: 'What should it be called?',
      default: 'Button',
      validate: (value) => {
        if (/.+/.test(value)) {
          return componentExists(value) ? 'A component or container with this name already exists' : true;
        }

        return 'The name is required';
      }
    }
  ],
  actions: (data) => {
    // Generate index.js and index.test.js
    const actions = [
      {
        type: 'add',
        path: '../../src/stories/components/{{properCase name}}/{{properCase name}}.stories.tsx',
        templateFile: './storybook/index.tsx.hbs',
        abortOnFail: true
      }
    ];

    actions.push({
      type: 'prettify',
      path: '/stories/components/'
    });

    return actions;
  }
};
