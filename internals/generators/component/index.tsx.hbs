/**
 *
 * {{ properCase name }}
 *
 */
{{#if memo}}
import React, { memo } from 'react';
{{else}}
import React from 'react';
{{/if}}

{{#if wantMessages}}
import { FormattedMessage } from 'react-intl';
import messages from './messages';
{{/if}}

interface {{ properCase name }}Props {}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
function {{ properCase name }}(props: {{ properCase name }}Props) {
  return (
  <div>
    {{#if wantMessages}}
    <FormattedMessage {...messages.header} />
    {{/if}}
  </div>
  );
};

{{!-- {{#if memo}} --}}
{{!-- export default memo({{ properCase name }}); --}}
{{!-- {{else}} --}}
export default {{ properCase name }};
{{!-- {{/if}} --}}
