/**
 *
 * Tests for {{ properCase name }}
 *
 * @see https://github.com/react-boilerplate/react-boilerplate/tree/master/docs/testing
 *
 */

import React from 'react';
import { render } from '@testing-library/react';
{{#if wantMessages}}
import { IntlProvider } from 'react-intl';
{{/if}}
// import 'jest-dom/extend-expect'; // add some helpful assertions

import {{ properCase name }} from '../index';
{{#if wantMessages}}
import { DEFAULT_LOCALE } from '../../../locales';
{{/if}}

describe('<{{ properCase name }} />', () => {
  it('Expect to not log errors in console', () => {
    const spy = jest.spyOn(global.console, 'error');
{{#if wantMessages}}
    render(
      <IntlProvider locale={DEFAULT_LOCALE}>
        <{{ properCase name }} />
      </IntlProvider>,
    );
{{else}}
    render(<{{ properCase name }} />);
{{/if}}
    expect(spy).not.toHaveBeenCalled();
  });

  it('Expect to have additional unit tests specified', () => {
    expect(true).toEqual(false);
  });

  /**
   * Unskip this test to use it
   *
   * @see {@link https://jestjs.io/docs/en/api#testskipname-fn}
   */
  it.skip('Should render and match the snapshot', () => {
{{#if wantMessages}}
    const {
      container: { firstChild },
    } = render(
      <IntlProvider locale={DEFAULT_LOCALE}>
        <{{ properCase name }} />
      </IntlProvider>,
    );
{{else}}
    const {
      container: { firstChild },
    } = render(<{{ properCase name }} />);
{{/if}}
    expect(firstChild).toMatchSnapshot();
  });
});
