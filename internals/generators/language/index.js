/**
 * Language Generator
 */
const fs = require('fs');
const { exec } = require('child_process');

function languageIsSupported(language) {
  try {
    fs.accessSync(`app/translations/${language}.json`, fs.F_OK);
    return true;
  } catch (e) {
    return false;
  }
}

module.exports = {
  description: 'Add a language',
  prompts: [
    {
      type: 'input',
      name: 'language',
      message: 'What is the language you want to add i18n support for (e.g. "fr", "de")?',
      default: 'fr',
      validate: (value) => {
        if (/.+/.test(value) && value.length === 2) {
          return languageIsSupported(value) ? `The language "${value}" is already supported.` : true;
        }

        return '2 character language specifier is required';
      }
    }
  ],

  actions: ({ test }) => {
    const actions = [];

    if (test) {
      // backup files that will be modified so we can restore them
      actions.push({
        type: 'backup',
        path: '../../src',
        file: 'i18n.ts'
      });

      actions.push({
        type: 'backup',
        path: '../../src',
        file: 'app.tsx'
      });
      actions.push({
        type: 'backup',
        path: '../../src',
        file: 'locales.ts'
      });
    }

    actions.push({
      type: 'modify',
      path: '../../src/i18n.ts',
      pattern: /(const ..LocaleData = require\('react-intl\/locale-data\/..'\);\n)+/g,
      templateFile: './language/intl-locale-data.hbs'
    });
    actions.push({
      type: 'modify',
      path: '../../src/locales.ts',
      pattern: /(\s+'[a-z]+',\n)(?!.*\s+'[a-z]+',)/g,
      templateFile: './language/src-locale.hbs'
    });
    actions.push({
      type: 'modify',
      path: '../../src/i18n.ts',
      pattern: /(const ..TranslationMessages = require\('\.\/translations\/..\.json'\);\n)(?!const ..TranslationMessages = require\('\.\/translations\/..\.json'\);\n)/g,
      templateFile: './language/translation-messages.hbs'
    });
    actions.push({
      type: 'modify',
      path: '../../src/i18n.ts',
      pattern: /(addLocaleData\([a-z]+LocaleData\);\n)(?!.*addLocaleData\([a-z]+LocaleData\);)/g,
      templateFile: './language/add-locale-data.hbs'
    });
    actions.push({
      type: 'modify',
      path: '../../src/i18n.ts',
      pattern: /([a-z]+:\sformatTranslationMessages\('[a-z]+',\s[a-z]+TranslationMessages\),\n)(?!.*[a-z]+:\sformatTranslationMessages\('[a-z]+',\s[a-z]+TranslationMessages\),)/g,
      templateFile: './language/format-translation-messages.hbs'
    });
    actions.push({
      type: 'add',
      path: '../../src/translations/{{language}}.json',
      templateFile: './language/translations-json.hbs',
      abortOnFail: true
    });
    actions.push({
      type: 'modify',
      path: '../../src/src.tsx',
      pattern: /(import\('intl\/locale-data\/jsonp\/[a-z]+\.js'\),\n)(?!.*import\('intl\/locale-data\/jsonp\/[a-z]+\.js'\),)/g,
      templateFile: './language/polyfill-intl-locale.hbs'
    });

    if (!test) {
      actions.push(() => {
        const cmd = 'npm run extract-intl';
        exec(cmd, (err, result) => {
          if (err) throw err;
          process.stdout.write(result);
        });
        return 'modify translation messages';
      });
    }

    return actions;
  }
};
