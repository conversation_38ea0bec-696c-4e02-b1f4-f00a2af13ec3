import React from 'react';

import OptionBar, { OptionBarProps } from 'vcomponents/OptionBar';

export default {
  title: 'vendys-components/OptionBar',
  component: OptionBar,
};

const Template: Story<OptionBarProps> = (args) => <OptionBar {...args} />;

enum e {
  a = 'a',
  b = 'b',
  c = 'c'
}

const template = [
  {id: 'a', type: 'enum', data: e , props: { existTotal: true}},
  {id: 'b', type: 'string'},
  {id: 'c', type: <div>sdfa</div>},
  {id: 'd', type: 'enum', props: {enumId: 'DeliveryStatus'} },
]

export const Common = Template.bind({});
Common.args = {
  template
};
