import moment from 'moment';
import { MEMBER_DIVISION, MEMBER_AUTH, MEMBER_GROUP, MEMBER_USER_INFO, MEMBER_RANK_POSITION, MEMBER_CLEAN } from 'actions/member';
import storage from 'helpers/storage';

function member(state = {}, action) {
  const { payload } = action;

  switch (action.type) {
    case `${MEMBER_DIVISION}_FULFILLED`:
      return {
        ...state,
        division: { ...payload.data }
      };
    case `${MEMBER_DIVISION}_REJECTED`:
      return {
        ...state,
        division: payload
      };
    case `${MEMBER_DIVISION}_PENDING`:
      return {
        ...state,
        division: payload
      };

    case `${MEMBER_AUTH}_FULFILLED`:
      return {
        ...state,
        auth: payload.data
      };
    case `${MEMBER_AUTH}_REJECTED`:
      return {
        ...state,
        auth: payload
      };
    case `${MEMBER_AUTH}_PENDING`:
      return {
        ...state,
        auth: payload
      };

    case `${MEMBER_GROUP}_FULFILLED`:
      return {
        ...state,
        group: payload.data.group
      };
    case `${MEMBER_GROUP}_REJECTED`:
      return {
        ...state,
        group: payload
      };
    case `${MEMBER_GROUP}_PENDING`:
      return {
        ...state,
        group: payload
      };

    case `${MEMBER_USER_INFO}_FULFILLED`:
      return {
        ...state,
        user: {
          ...payload.data.user,
          captainPaymentGrade: payload.data.user.captainPaymentGrade.type,
          groupIdx: payload.data.user.group ? payload.data.user.group.groupIdx : payload.data.user.group,
          birthday: payload.data.user.birthday ? moment(payload.data.user.birthday) : payload.data.user.birthday,
          rankPosition: payload.data.user.rankPosition && payload.data.user.rankPosition.id,
          welfareGroupIdx: payload.data.user.welfareGroup
            ? payload.data.user.welfareGroup.groupIdx
            : payload.data.user.welfareGroup
        }
      };
    case `${MEMBER_USER_INFO}_REJECTED`:
      return {
        ...state,
        user: payload
      };
    case `${MEMBER_USER_INFO}_PENDING`:
      return {
        ...state,
        user: payload
      };
      case `MEMBER_CLEAN`:
        return {
          ...state,
          user: undefined
        };


    case `${MEMBER_RANK_POSITION}_FULFILLED`:
      return {
        ...state,
        position: payload.data.rankPosition
      };
    case `${MEMBER_RANK_POSITION}_REJECTED`:
      return {
        ...state,
        position: payload
      };
    case `${MEMBER_RANK_POSITION}_PENDING`:
      return {
        ...state,
        position: payload
      };

    default:
      return state;
  }
}

export default member;
