import {
  CAPTAIN_PAYMENT,
  CAPTAIN_PAYMENT_PERMISSION,
  CAPTAIN_PAYMENT_SIKDAE_PERMISSION,
  CAPTAIN_PAYMENT_SIKDAE_PERMISSION_DENIED
} from 'actions/captainPayment';

const initState = {
  user: {
    id: '',
    captainPaymentGrade: '',
    name: ''
  },
  companyInfo: {
    comId: '',
    name: '',
    officeList: [],
    serviceType: []
  },
  serviceMenuList: [],
  pending: false,
  error: '',
  menuPermission: {},
  sikdaePermission: undefined
};
const idMapping = (data, key) => data.reduce((acc, e, i) => ({ ...acc, [e[key]]: i }), {});
const setMenuListTree = (menus = []) => {
  const loot = [];
  const mapping = idMapping(menus);
  // eslint-disable-next-line no-restricted-syntax
  for (const menu of menus) {
    if (!menu.parentMenuIdx) {
      loot.push(menu);
    } else {
      const parent = menus[mapping[menu.parentMenuIdx]];
      parent.children = [...(parent.children || []), menu];
    }
  }
  return loot;
};

function captainPayment(state = initState, action) {
  const { payload } = action;
  switch (action.type) {
    case `${CAPTAIN_PAYMENT}_FULFILLED`:
      return {
        ...state,
        ...payload,
        serviceMenuList: payload.serviceMenuList.reduce(
          (acc, { serviceType, menuList }) => ({ ...acc, [serviceType.toLowerCase()]: setMenuListTree(menuList) }),
          {}
        ),
        pending: false
      };
    case `${CAPTAIN_PAYMENT}_PENDING`:
      return {
        ...state,
        ...payload,
        pending: true
      };
    case `${CAPTAIN_PAYMENT}_REJECTED`:
      return {
        ...state,
        ...payload.error,
        pending: false
      };
    case `${CAPTAIN_PAYMENT_PERMISSION}_FULFILLED`:
      return {
        ...state,
        menuPermission: {
          [payload.menu.idx]: payload.menu
        },
        pending: false
      };
    case `${CAPTAIN_PAYMENT_PERMISSION}_PENDING`:
      return {
        ...state,
        pending: true
      };
    case `${CAPTAIN_PAYMENT_PERMISSION}_REJECTED`:
      return {
        ...state,
        pending: false
      };

    case `${CAPTAIN_PAYMENT_SIKDAE_PERMISSION}_FULFILLED`:
      return {
        ...state,
        sikdaePermission: true,
        pending: false
      };
    case `${CAPTAIN_PAYMENT_SIKDAE_PERMISSION}_PENDING`:
      return {
        ...state,
        pending: true
      };
    case `${CAPTAIN_PAYMENT_SIKDAE_PERMISSION}_REJECTED`:
      return {
        ...state,
        sikdaePermission: false,
        pending: false
      };
    case `${CAPTAIN_PAYMENT_SIKDAE_PERMISSION_DENIED}`:
      return {
        ...state,
        sikdaePermission: false
      };
    default:
      return state;
  }
}

export default captainPayment;
