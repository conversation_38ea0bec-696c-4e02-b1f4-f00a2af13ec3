import { createAction } from 'redux-actions';
import * as dashboard from 'services/dashboard';

export const MONTHLY_STATUS = 'MONTHLY_STATUS';
export const DAILY_STATUS = 'DAILY_STATUS';
export const HOURLY_STATUS = 'HOURLY_STATUS';
export const STORE_TOP = 'STORE_TOP';
export const DEPARTMENT_TOP = 'DEPARTMENT_TOP';
export const MENU_TOP = 'MENU_TOP';
export const DORMATN_RESERVED_CNT = 'DORMATN_RESERVED_CNT';

// 월별 식대 금액
export const MonthlyStatus = () => ({
  type: MONTHLY_STATUS,
  payload: {
    promise: dashboard.MonthlyStatus()
  }
});

// 일별 식대 금액
export const DailyStatus = () => ({
  type: DAILY_STATUS,
  payload: {
    promise: dashboard.DailyStatus()
  }
});

// 시간대별 사용 현황
export const HourlyStatus = (params) => ({
  type: HOURLY_STATUS,
  payload: {
    promise: dashboard.HourlyStatus(params)
  }
});

// 가맹점 사용금액 TOP 5
export const StoreTopList = () => ({
  type: STORE_TOP,
  payload: {
    promise: dashboard.StoreTopList()
  }
});

// 부서 사용금액 TOP 5
export const DepartmentTopList = () => ({
  type: DEPARTMENT_TOP,
  payload: {
    promise: dashboard.DepartmentTopList()
  }
});

// 메뉴 TOP 5
export const MenuTopList = () => ({
  type: MENU_TOP,
  payload: {
    promise: dashboard.MenuTopList()
  }
});

// 휴면 예정 사용자
export const DormantReservedCnt = (params) => ({
  type: DORMATN_RESERVED_CNT,
  payload: {
    promise: dashboard.dormantReservedCount(params)
  }
});
