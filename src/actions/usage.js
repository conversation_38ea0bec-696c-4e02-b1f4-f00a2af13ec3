import * as usage from 'services/usage';
import { MemberClean } from './member';

export const MYPOINT_TYPE_LIST = 'MYPOINT_TYPE_LIST';
export const MYPOINT_STATUS_LIST = 'MYPOINT_STATUS_LIST';
export const MYPOINT_LIST = 'MYPOINT_LIST';
export const USER_SIKDAE_LIST = 'USER_SIKDAE_LIST';
export const USER_SIKDAE_TOTAL = 'USER_SIKDAE_TOTAL';
export const USER_SIKDAE_DETAIL = 'USER_SIKDAE_DETAIL';
export const USER_SIKDAE_USAGE_LIST = 'USER_SIKDAE_USAGE_LIST';
export const USER_SIKDAE_USAGE_TOTAL = 'USER_SIKDAE_USAGE_TOTAL';
export const USER_SIKDAE_USAGE_DETAIL = 'USER_SIKDAE_USAGE_DETAIL';
export const CLEAN_USER_SIKDAE_USAGE = 'CLEAN_USER_SIKDAE_USAGE';
export const USER_SIKDAE_GRAND_DEDUCT_LIST = 'USER_SIKDAE_GRAND_DEDUCT_LIST';
export const USER_SIKDAE_GRAND_DEDUCT_TOTAL = 'USER_SIKDAE_GRAND_DEDUCT_TOTAL';
export const USER_SIKDAE_GRAND_DEDUCT_DETAIL = 'USER_SIKDAE_GRAND_DEDUCT_DETAIL';
export const CLEAN_USER_SIKDAE_GRAND_DEDUCT = 'CLEAN_USER_SIKDAE_GRAND_DEDUCT';
export const STATS_STORE = 'STATS_STORE';
export const STATS_DIVISION = 'STATS_DIVISION';
export const STATS_GROUP = 'STATS_GROUP';
export const STATS_USAGE = 'STATS_USAGE';
export const STATS_USAGE_TOTAL = 'STATS_USAGE_TOTAL';
export const DEDUCTION_SAMSUNGCARD_STATS_USAGE = 'DEDUCTION_SAMSUNGCARD_STATS_USAGE';
export const DEDUCTION_SAMSUNGCARD_STATS_USAGE_TOTAL = 'DEDUCTION_SAMSUNGCARD_STATS_USAGE_TOTAL';
export const DEDUCTION_SAMSUNGCNT_STATS_USAGE = 'DEDUCTION_SAMSUNGCNT_STATS_USAGE';
export const DEDUCTION_SAMSUNGMEDICAL_STATS_USAGE = 'DEDUCTION_SAMSUNGMEDICAL_STATS_USAGE';
export const DEDUCTION_KIMANDCHANG_STATS_USAGE = 'DEDUCTION_KIMANDCHANG_STATS_USAGE';
export const SIKDAE_DETAIL_LIST = 'SIKDAE_DETAIL_LIST';
export const SIKDAE_DETAIL_EXCEL_LIST = 'SIKDAE_DETAIL_EXCEL_LIST';
export const SIKDAE_DETAIL_DETAIL = 'SIKDAE_DETAIL_DETAIL';
export const SIKDAE_DETAIL_LIST_DOWNLOAD = 'SIKDAE_DETAIL_LIST_DOWNLOAD';
export const BILL_DATE_LIST = 'BILL_DATE_LIST';
export const BILL_GROUP_LIST = 'BILL_GROUP_LIST';
export const BILL_DETAIL = 'BILL_DETAIL';
export const BILL_ACCEPTED = 'BILL_ACCEPTED';
export const BILL_REJECTED = 'BILL_REJECTED';
export const BILL_PARENT_REJECTED = 'BILL_PARENT_REJECTED';
export const BILL_DET_LIST = 'BILL_DET_LIST';
export const BILL_DET_TOTAL = 'BILL_DET_TOTAL';
export const BILL_APPROVAL_STEP_LIST = 'BILL_APPROVAL_STEP_LIST';
export const BILL_APPROVAL_STEP_MOD = 'BILL_APPROVAL_STEP_MOD';
export const BILL_RETURN_STEP_MOD = 'BILL_RETURN_STEP_MOD';
export const OURHOME_SUBTRACT_STATS_LIST = 'OURHOME_SUBTRACT_STATS_LIST';
export const INITIALIZE_USAGE_STATS = 'INITIALIZE_USAGE_STATS';

// 대장포인트 사용타입 코드
export const MyPointTypeList = () => ({
  type: MYPOINT_TYPE_LIST,
  payload: {
    promise: usage.MyPointTypeList()
  }
});

// 대장포인트 상태 코드
export const MyPointStatusList = () => ({
  type: MYPOINT_STATUS_LIST,
  payload: {
    promise: usage.MyPointStatusList()
  }
});

// 대장포인트 내역
export const MyPointList = (params) => ({
  type: MYPOINT_LIST,
  payload: {
    promise: usage.MyPointList(params)
  }
});

// 사용자별 식대 지급/사용 내역
export const UserSikdaeGivenList = (params) => ({
  type: USER_SIKDAE_LIST,
  payload: {
    promise: usage.UserSikdaeGivenList(params)
  }
});

// 사용자별 식대 지급/사용 합계
export const UserSikdaeGivenTotal = (params) => ({
  type: USER_SIKDAE_TOTAL,
  payload: {
    promise: usage.UserSikdaeGivenTotal(params)
  }
});

// 사용자별 식대 지급/사용 상세
export const UserSikdaeGivenDtl = (id, params) => ({
  type: USER_SIKDAE_DETAIL,
  payload: {
    promise: usage.UserSikdaeGivenDtl(id, params)
  }
});

// 사용자별 식대 사용 내역 v2
export const UserSikdaeUsageHistory = (params) => ({
  type: USER_SIKDAE_USAGE_LIST,
  payload: {
    promise: usage.UserSikdaeUsageHistory(params)
  }
});

// 사용자별 식대 사용 합계
export const UserSikdaeUsageTotal = (params) => ({
  type: USER_SIKDAE_USAGE_TOTAL,
  payload: {
    promise: usage.UserSikdaeUsageTotal(params)
  }
});

// 사용자별 식대 사용 상세
export const UserSikdaeUsageDetail = (id, params) => ({
  type: USER_SIKDAE_USAGE_DETAIL,
  payload: {
    promise: usage.UserSikdaeUsageDetail(id, params)
  }
});

export const CleanUserSikdaeUsage = () => ({
  type: CLEAN_USER_SIKDAE_USAGE
});

// 사용자별 식대 지급/차감 내역
export const UserSikdaeGrandDeductHistory = (params) => ({
  type: USER_SIKDAE_GRAND_DEDUCT_LIST,
  payload: {
    promise: usage.UserSikdaeGrandDeductHistory(params)
  }
});
// 사용자별 식대 지급/차감 합계
export const UserSikdaeGrandDeductTotal = (params) => ({
  type: USER_SIKDAE_GRAND_DEDUCT_TOTAL,
  payload: {
    promise: usage.UserSikdaeGrandDeductTotal(params)
  }
});
// 사용자별 식대 지급/차감 상세
export const UserSikdaeGrandDeductDetail = (id, params) => ({
  type: USER_SIKDAE_GRAND_DEDUCT_DETAIL,
  payload: {
    promise: usage.UserSikdaeGrandDeductDetail(id, params)
  }
});
export const CleanSikdaeGrandDeduct = () => ({
  type: CLEAN_USER_SIKDAE_GRAND_DEDUCT
});
// 가맹점 통계
export const StoreStatsList = (params) => ({
  type: STATS_STORE,
  payload: {
    promise: usage.StoreStatsList(params)
  }
});

// 부서 통계
export const DivisionStatsList = (params) => ({
  type: STATS_DIVISION,
  payload: {
    promise: usage.DivisionStatsList(params)
  }
});

// 식대그룹 통계
export const GroupStatsList = (params) => ({
  type: STATS_GROUP,
  payload: {
    promise: usage.GroupStatsList(params)
  }
});

// 식대사용 통계
export const UsageStatsList = (params) => ({
  type: STATS_USAGE,
  payload: {
    promise: usage.UsageStatsList(params)
  }
});

// 식대사용 통계 합계
export const UsageStatsTotal = (params) => ({
  type: STATS_USAGE_TOTAL,
  payload: {
    promise: usage.UsageStatsTotal(params)
  }
});

// 식대 사용내역 > 삼성물산 급여공제
export const DeductionSamsungCNTUsageStatsList = (params) => ({
  type: DEDUCTION_SAMSUNGCNT_STATS_USAGE,
  payload: {
    promise: usage.DeductionSamsungCNTUsageStatsList(params)
  }
});

// 식대 사용내역 > 삼성카드 급여공제
export const DeductionSamsungCardUsageStatsList = (params) => ({
  type: DEDUCTION_SAMSUNGCARD_STATS_USAGE,
  payload: {
    promise: usage.DeductionSamsungCardUsageStatsList(params)
  }
});

// 식대 사용내역 > 삼성카드 급여공제 합계
export const DeductionSamsungCardUsageStatsTotal = (params) => ({
  type: DEDUCTION_SAMSUNGCARD_STATS_USAGE_TOTAL,
  payload: {
    promise: usage.DeductionSamsungCardUsageStatsTotal(params)
  }
});

// 식대 사용내역 > 삼성의료재단 급여공제
export const DeductionSamsungMedicalUsageStatsList = (params) => ({
  type: DEDUCTION_SAMSUNGMEDICAL_STATS_USAGE,
  payload: {
    promise: usage.DeductionSamsungMedicalUsageStatsList(params)
  }
});

// 식대 사용내역 > 김앤장 급여공제
export const DeductionKimAndChangUsageStatsList = (params) => ({
  type: DEDUCTION_KIMANDCHANG_STATS_USAGE,
  payload: {
    promise: usage.DeductionKimAndChangUsageStatsList(params)
  }
});

// 식대상세 조회
export const SikdaeDtlList = (params) => ({
  type: SIKDAE_DETAIL_LIST,
  payload: {
    promise: usage.SikdaeDtlList(params)
  }
});
// 식대상세 엑셀용 리스트 조회
export const SikdaeDtlExcelList = (params, config) => ({
  type: SIKDAE_DETAIL_EXCEL_LIST,
  payload: {
    promise: usage.SikdaeDtlExcelList(params, config)
  }
});

// 식대상세 조회 - 상세
export const SikdaeListDtl = (couponid) => ({
  type: SIKDAE_DETAIL_DETAIL,
  payload: {
    promise: usage.SikdaeListDtl(couponid)
  }
});

// 식대상세 조회 엑셀 다운로드
export const ExcelSikdaeDtlList = (params) => ({
  type: SIKDAE_DETAIL_LIST_DOWNLOAD,
  payload: {
    promise: usage.ExcelSikdaeDtlList(params)
  }
});

// 청구서 내역 조회
export const BillDtl = (idx, params) => ({
  type: BILL_DETAIL,
  payload: {
    promise: usage.BillDtl(idx, params)
  }
});

// 청구서 승인
export const BillAccepted = (method, data, pIdx, iIdx) => ({
  type: BILL_ACCEPTED,
  payload: {
    promise: usage.BillAccepted(method, data, pIdx, iIdx)
  }
});

// 청구서 반려
export const BillRejected = (data, pIdx, iIdx) => ({
  type: BILL_REJECTED,
  payload: {
    promise: usage.BillRejected(data, pIdx, iIdx)
  }
});

export const BillParentRejected = (data, pIdx, iIdx) => ({
  type: BILL_PARENT_REJECTED,
  payload: {
    promise: usage.BillParentRejected(data, pIdx, iIdx)
  }
});

// 청구서 목록 조회
export const BillGroupList = (params) => ({
  type: BILL_GROUP_LIST,
  payload: {
    promise: usage.BillGroupList(params)
  }
});

// 청구서 목록 조회
export const BillDateList = () => ({
  type: BILL_DATE_LIST,
  payload: {
    promise: usage.BillDateList()
  }
});

// 청구서 식대 상세 조회
export const BillDetList = (iIdx, params) => ({
  type: BILL_DET_LIST,
  payload: {
    promise: usage.BillDetList(iIdx, params)
  }
});

// 청구서 식대 상세 total
export const BillDetTotal = (iIdx) => ({
  type: BILL_DET_TOTAL,
  payload: {
    promise: usage.BillDetTotal(iIdx)
  }
});

// 청구서 결재 라인 조회
export const BillApprovalStepList = (iIdx, params) => ({
  type: BILL_APPROVAL_STEP_LIST,
  payload: {
    promise: usage.BillApprovalStepList(iIdx, params)
  }
});

// 청구서 결재 라인 수정
export const BillApprovalStepMod = (iIdx, params) => ({
  type: BILL_APPROVAL_STEP_MOD,
  payload: {
    promise: usage.BillApprovalStepMod(iIdx, params)
  }
});

// 청구서 재발행
export const BillReturn = (data, pIdx, iIdx) => ({
  type: BILL_RETURN_STEP_MOD,
  payload: {
    promise: usage.BillReturnStepMod(data, pIdx, iIdx)
  }
});

// 아워홈 포인트 차감 통계
export const OurhomeStatsList = (params) => ({
  type: OURHOME_SUBTRACT_STATS_LIST,
  payload: {
    promise: usage.OurhomeSubtractStatsList(params)
  }
});

// 사용 통계 초기화
export const initializeUsageStats = () => ({
  type: INITIALIZE_USAGE_STATS
});
