import * as auth from 'services/auth';

export const TOKEN_CHECK = 'TOKEN_CHECK';
export const ME_COMPANY_INFO = 'ME_COMPANY_INFO';
export const ME_PAYMENT_INFO = 'ME_PAYMENT_INFO';
export const ME_SERVICE_SIKDAE_INFO = 'ME_SERVICE_SIKDAE_INFO';
export const CHANGE_PASSWORD = 'CHANGE_PASSWORD';
export const CHANGE_PASSWORD_IGNORE = 'CHANGE_PASSWORD';

export const authAPI = (params) => ({
  type: TOKEN_CHECK,
  payload: {
    promise: auth.authAPI(params)
  }
});

export const changePasswordAPI = (params) => ({
  type: CHANGE_PASSWORD,
  payload: {
    promise: auth.changePasswordAPI(params)
  }
});

export const changePasswordIgnoreAPI = () => ({
  type: CHANGE_PASSWORD_IGNORE,
  payload: {
    promise: auth.changePasswordIgnoreAPI()
  }
});

export const meAPI = (params) => ({
  type: ME_COMPANY_INFO,
  payload: {
    promise: auth.meAPI(params)
  }
});

export const mePaymentAPI = (params) => ({
  type: ME_PAYMENT_INFO,
  payload: {
    promise: auth.mePaymentAPI(params)
  }
});

export const serviceSikdaeAPI = (params) => ({
  type: ME_SERVICE_SIKDAE_INFO,
  payload: {
    promise: auth.serviceSikdaeAPI(params)
  }
});
