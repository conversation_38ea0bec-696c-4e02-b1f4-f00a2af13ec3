import * as holiday from 'services/holiday';

export const HOLIDAY_COMPANY_LIST = 'HOLIDAY_COMPANY_LIST';
export const HOLIDAY_COMPANY_INFO = 'HOLIDAY_COMPANY_INFO';
export const HOLIDAY_COMPANY_SAVE = 'HOLIDAY_COMPANY_SAVE';

// 공휴일 목록 조회
export const HolidayCompanyInfoList = (year) => ({
  type: HOLIDAY_COMPANY_LIST,
  payload: {
    promise: holiday.HolidayCompanyInfoList(year)
  }
});

// 공휴일 상세 정보 조회
export const HolidayCompanyInfo = (holidayIdx) => ({
  type: HOLIDAY_COMPANY_INFO,
  payload: {
    promise: holiday.HolidayCompanyInfo(holidayIdx)
  }
});

export const HolidayCompanySave = (data) => ({
  type: HOLIDAY_COMPANY_SAVE,
  payload: {
    promise: holiday.HolidayCompanySave(data)
  }
});
