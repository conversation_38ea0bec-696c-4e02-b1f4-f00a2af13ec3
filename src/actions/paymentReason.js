import * as paymentReason from 'services/paymentReason';
import { createAction } from 'redux-actions';

export const PAYMENT_REASON_LIST = 'PAYMENT_REASON_LIST';
export const PAYMENT_REASON_UPDATE = 'PAYMENT_REASON_UPDATE';
export const PAYMENT_REASON_FILTER = 'PAYMENT_REASON_FILTER';
export const PAYMENT_REASON_CHECK = 'PAYMENT_REASON_CHECK';
export const PAYMENT_REASON_CHECK_ALL = 'PAYMENT_REASON_CHECK_ALL';

export const getPaymentReasonList = (params) => ({
  type: PAYMENT_REASON_LIST,
  payload: {
    promise: paymentReason.paymentReasonList(params)
  }
});

export const updatePaymentReasons = (params) => ({
  type: PAYMENT_REASON_UPDATE,
  payload: {
    promise: paymentReason.updatePaymentReason(params)
  }
});

export const setPaymentReasonFilter = createAction(PAYMENT_REASON_FILTER);
export const checkedPaymentReason = createAction(PAYMENT_REASON_CHECK);
export const checkedAllPaymentReason = createAction(PAYMENT_REASON_CHECK_ALL);
