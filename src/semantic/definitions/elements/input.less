/*******************************
          Vendys Theme
*******************************/

@type    : 'element';
@element : 'input';

@import (multiple) '../../theme.config';

/*******************************
            Input
*******************************/

.ui.form input:not([type]),
.ui.form input[type=date],
.ui.form input[type=datetime-local],
.ui.form input[type=email],
.ui.form input[type=file],
.ui.form input[type=number],
.ui.form input[type=password],
.ui.form input[type=search],
.ui.form input[type=tel],
.ui.form input[type=text],
.ui.form input[type=time],
.ui.form input[type=url] {
  border-radius: 0;
  .rem-px(height, @formSize);
  // .rem-px(line-height, @formSize+(-2));
  .rem-px(line-height, 1);
  .rem-px(font-size, @vendysH6);
}

.ui.form input:not([type]):focus,
.ui.form input[type=date]:focus,
.ui.form input[type=datetime-local]:focus,
.ui.form input[type=email]:focus,
.ui.form input[type=file]:focus,
.ui.form input[type=number]:focus,
.ui.form input[type=password]:focus,
.ui.form input[type=search]:focus,
.ui.form input[type=tel]:focus,
.ui.form input[type=text]:focus,
.ui.form input[type=time]:focus,
.ui.form input[type=url]:focus {
  border-radius: 0;
}

.ui.form textarea:focus {
  .rem-px(border-radius, 4);
}

.ui.input input {
  border-radius: 0;
  border-color: @outlineColor;
}
