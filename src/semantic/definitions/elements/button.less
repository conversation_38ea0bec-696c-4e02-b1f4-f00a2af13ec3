/*!
 * # Semantic UI - Button
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type: 'element';
@element: 'button';

@import (multiple) '../../theme.config';

/*******************************
            Button
*******************************/

.ui.buttons .disabled.button,
.ui.disabled.button,
.ui.button:disabled,
.ui.disabled.button:hover,
.ui.disabled.active.button {
  background: @btnDisableBackgroundColor;
  color: @btnDisableColor;
}

.ui.green.button,
.ui.black.buttons .button {
  background-color: @greenButtonColor;
}

.ui.inverted.green.button,
.ui.inverted.green.buttons .button {
  .foo(1);
  box-shadow: inset 0 0 0 @return @greenButtonColor !important;
  color: @greenButtonColor;
}

.ui.black.button,
.ui.black.buttons .button {
  background-color: @blackButtonColor;
}

.ui.inverted.black.button,
.ui.inverted.black.buttons .button {
  .foo(1);
  box-shadow: inset 0 0 0 @return @blackButtonColor !important;
  color: @blackButtonColor;
}

.ui.inverted.black.button:focus {
  box-shadow: inset 0 0 0 0.07142857rem #243346 !important;
  color: #000000 !important;
  background: #ffffff;
}

.ui.inverted.black.button:hover {
  box-shadow: inset 0 0 0 0.07142857rem #243346 !important;
  color: #ffffff !important;
  background: #000000;
}

.ui.inverted.red.button,
.ui.inverted.red.buttons .button {
  .foo(1);
  box-shadow: inset 0 0 0 @return @redButtonColor !important;
  color: @redButtonColor;
}

.ui.inverted.blue.button,
.ui.inverted.blue.buttons .button {
  box-shadow: inset 0 0 0 1px @blue !important;
  color: @blue;
}

/* Inverted */
.ui.inverted.grey.buttons .button,
.ui.inverted.grey.button {
  .foo(0, 0, 0, 1);
  box-shadow: @return @greySolidBorderColor inset !important;
  color: @greyInvertedTextColor;
}

.ui.button {
  border-radius: 0;
  margin: 0;
  min-height: 2rem;
}
.ui.buttons .button:first-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.ui.buttons .button:last-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.ui.basic.buttons {
  border-color: @blackButtonColor;
  border-radius: 0;
}

.ui.basic.buttons,
.ui.basic.buttons .button {
  color: @blackButtonColor !important;
  font-weight: bold;
}

.ui.basic.button,
.ui.basic.buttons .button {
  border-radius: 0;
}

.ui.basic.buttons .button {
  border-color: @blackButtonColor;
}

.ui.buttons .button:first-child {
  border-left: none;
}

.ui.basic.active.button,
.ui.basic.buttons .active.button {
  background: @blackButtonColor !important;
  color: @white !important;
}

.ui.labeled.icon.button {
  background-color: @white;
  border: 1px solid @outlineColor;
}

.ui.labeled.icon.button > .icon {
  background-color: @conerLabelColor;
  border-right: 1px solid @outlineColor;
  color: @conerLabelIconColor;
}

.ui.action.input:not([class*='left action']) > .button:last-child,
.ui.action.input:not([class*='left action']) > .buttons:last-child > .button,
.ui.action.input:not([class*='left action']) > .dropdown:last-child {
  border-radius: 0;
}

.ui.inverted.red.buttons .button,
.ui.inverted.red.button {
  .foo(1);
  box-shadow: 0px 0px 0px @return @lightRed inset !important;
}

.ui.loading.green.loading.loading.loading.loading.loading.button {
  color: @white !important;
  background-color: @greenButtonColor !important;
}
.ui.loading.black.loading.loading.loading.loading.loading.button {
  color: @blackButtonColor !important;
}

// .ui.loading.green.loading.loading.loading.loading.loading.button:hover {
//   color : @white !important;
// }

.ui.loading.button:after,
.ui.loading.button:before {
  left: 10%;
}

.loading-center:after,
.loading-center:before {
  left: 50% !important;
}
