import request from 'helpers/request';
import api from 'config';

//회사 정보 조회
export const CompanyInfoSel = (comId) =>
  request({
    url: api.config.companyAPI + '/settlement/v1/company/' + comId
  });

//사용자 회원가입 목록 추가
export const CheckStaffList = (params) =>
  request({
    method: 'post',
    url: api.config.companyAPI + '/member/v1/signup/check',
    data: params
  });

//사용자 회원가입 링크전송
export const StaffLinkAdd = (params) =>
  request({
    method: 'post',
    url: api.config.companyAPI + '/member/v1/signup',
    data: params
  });

//사용자 회원가입 상태코드
export const SignupStatus = () =>
  request({
    url: api.config.companyAPI + '/member/v1/signup/status'
  });

//사용자 회원가입 요청내역
export const RequestedList = (params) =>
  request({
    url: api.config.companyAPI + '/member/v1/signup',
    params: params
  });

//사용자 회원가입 링크 재전송
export const ResendLink = (params) =>
  request({
    method: 'put',
    url: api.config.companyAPI + '/member/v1/signup',
    data: params
  });

//사용자 회원가입 링크 취소
export const CancelLink = (params) =>
  request({
    method: 'delete',
    url: api.config.companyAPI + '/member/v1/signup',
    data: params
  });

//사용자 추가, 변경
export const StaffAct = (method, params) => {
  let url = '/member/v1/employee';

  if (method === 'put') {
    url += '/' + params.id;
  }

  return request({
    method: method,
    url: api.config.companyAPI + url,
    data: params
  });
};

//사용자 퇴사
export const StaffDel = (params) =>
  request({
    method: 'delete',
    url: api.config.companyAPI + '/member/v1/employee/' + params.id
  });

//사용자 비밀번호 변경 (초기화)
export const StaffPwdChange = (params) =>
  request({
    method: 'put',
    url: api.config.companyAPI + '/member/v1/employee/' + params.id + '/password',
    data: params
  });

//사용자 비밀번호 변경
export const MyPwdChange = (params) => {
  const requestParam = {
    source: params.source,
    target: params.target
  };
  return request({
    method: 'put',
    url: api.config.companyAPI + '/member/v1/employee/' + params.id + '/password/change',
    data: requestParam
  });
};

//사용자 목록 엑셀파일로 관리 > 업로드 체크
export const StaffExcelCheck = (params) =>
  request({
    method: 'post',
    url: api.config.companyAPI + '/member/v1/employee/upload/check',
    data: params,
    type: 'file'
  });

//사용자 목록 엑셀파일로 관리 > 업로드
export const StaffExcelUpload = (params) =>
  request({
    method: 'post',
    url: api.config.companyAPI + '/member/v1/employee/upload',
    data: params,
    type: 'file'
  });

//직위 조회
export const PositionSel = () =>
  request({
    url: api.config.companyAPI + '/company/v1/rankposition'
  });

//직위 등록
export const PositionAdd = (params) =>
  request({
    method: 'post',
    url: api.config.companyAPI + '/company/v1/rankposition/',
    data: params
  });

//직위 수정
export const PositionMod = (params) =>
  request({
    method: 'put',
    url: api.config.companyAPI + '/company/v1/rankposition/' + params.id,
    data: params
  });

//직위 삭제
export const PositionDel = (params) =>
  request({
    method: 'delete',
    url: api.config.companyAPI + '/company/v1/rankposition/' + params
  });

//부서 재정렬
export const DivisionSortMod = (params) =>
  request({
    method: 'put',
    url: api.config.companyAPI + '/company/v1/division/' + params.id,
    data: params
  });

export const DivisionAdd = (params) =>
  request({
    method: 'post',
    url: api.config.companyAPI + '/company/v1/division',
    data: params
  });

//부서 삭제
export const DivisionDel = (params) =>
  request({
    method: 'delete',
    url: api.config.companyAPI + '/company/v1/division/' + params.id
  });

//회사 공지사항 목록
export const NoticeList = (params) =>
  request({
    url: api.config.companyAPI + '/addons/v1/notice',
    params: params
  });

//회사 공지사항 등록
export const NoticeAdd = (params) =>
  request({
    method: 'post',
    url: api.config.companyAPI + '/addons/v1/notice',
    data: params
  });

//회사 공지사항 상세
export const NoticeDtl = (id) =>
  request({
    url: api.config.companyAPI + '/addons/v1/notice/' + id
  });

//회사 공지사항 수정
export const NoticeMod = (id, params) =>
  request({
    method: 'put',
    url: api.config.companyAPI + '/addons/v1/notice/' + id,
    data: params
  });

//푸쉬 목록
export const PushMessageList = (params) =>
  request({
    url: api.config.companyAPI + '/addons/v1/push',
    params: params
  });

//푸쉬 등록
export const PushMessageAdd = (params) =>
  request({
    method: 'post',
    url: api.config.companyAPI + '/addons/v1/push',
    data: params
  });

//사용자 변경 (부서, 직위, 그룹별) 대량 변경
export const StaffBulkMod = (params) =>
  request({
    method: 'put',
    url: api.config.companyAPI + '/member/v1/employee',
    data: params
  });

//부서별 사용자 수
export const DivisionUserCnt = (params) =>
  request({
    url: api.config.companyAPI + '/company/v1/division/usercnt',
    params: params
  });

export const DivisionBudget = (orgCode, params) =>
  request({
    url: api.config.companyAPI + `/company/v1/budget?orgCode=${orgCode}`,
    params: params
  });

export const DivisionBudgetAdd = (params) =>
  request({
    url: api.config.companyAPI + '/company/v1/budget',
    method: 'post',
    data: params
  });

export const DivisionBudgetMod = (budgetIdx, params) =>
  request({
    url: api.config.companyAPI + `/company/v1/budget/${budgetIdx}`,
    method: 'put',
    data: params
  });

export const DivisionBudgetDel = (budgetIdx, params) =>
  request({
    url: api.config.companyAPI + `/company/v1/budget/${budgetIdx}`,
    method: 'delete',
    params: params
  });

export const DivisionBudgetSel = (budgetIdx, params) =>
  request({
    url: api.config.companyAPI + `/company/v1/budget/${budgetIdx}/usage`,
    params: params
  });

//강제 로그아웃(APP)
export const Logout = (id, params) =>
  request({
    url: api.config.authAPI + '/oauth2/v1/' + id + '/tokens',
    params: params
  });

export const LogoutStepTwo = (params) =>
  request({
    method: 'delete',
    url: api.config.authAPI + '/oauth2/tokens/' + params.token
  });

// 사용자 관리 정보변경 내역
export const ChangeInfoSel = (params) =>
  request({
    url: api.config.companyAPI + '/history/v1/info',
    params
  });

// 사용자 관리 정보 변경 내역 예약 취소
export const CancelReserve = (idx, params) =>
  request({
    method: 'put',
    url: api.config.companyAPI + '/history/v1/info/' + idx,
    data: params
  });

export const loginHistory = (params) =>
  request({
    url: `${api.config.companyAPI}/history/v1/login`,
    params
  });
