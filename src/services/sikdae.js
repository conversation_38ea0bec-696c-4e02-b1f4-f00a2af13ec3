import request from 'helpers/request';
import api from 'config';

//사용자 목록 (그룹 전체)
export const StaffGroupAllSel = (params) =>
  request({
    url: api.config.companyAPI + '/member/v1/group',
    params: params
  });

//식대내역 상태코드
export const SikdaeStatusList = () =>
  request({
    url: api.config.companyAPI + '/coupon/v1/task/status'
  });

//식대내역 목록
export const SikdaeGivenList = (params) =>
  request({
    url: api.config.companyAPI + '/coupon/v1/task',
    params: params
  });

//식대내역 상세
export const SikdaeGivenDetail = (id, params) =>
  request({
    url: api.config.companyAPI + '/coupon/v1/task/' + id,
    params: params
  });

//지급예약 취소
export const CancelReservation = (id) =>
  request({
    method: 'delete',
    url: api.config.companyAPI + '/coupon/v1/task/' + id
  });

//식권 신청 상태 코드
export const MealApprovalStatus = () =>
  request({
    url: api.config.companyAPI + '/coupon/v1/demand/status'
  });

// 식권 신청 결재 승인자 등록
export const MealAccepterMod = (dIdx, params) =>
  request({
    method: 'put',
    url: api.config.companyAPI + '/coupon/v1/demand/' + dIdx + '/accepter',
    data: params
  });

//식권 신청 결재 승인자
export const MealApprovalAccepterList = (dIdx, params) =>
  request({
    url: api.config.companyAPI + '/coupon/v1/demand/' + dIdx + '/accepter',
    params: params
  });

//식권 신청 내역
export const MealApprovalList = (params) =>
  request({
    url: api.config.companyAPI + '/coupon/v1/demand',
    params: params
  });

//식권 신청 반려
export const RejectMeal = (params) =>
  request({
    method: 'put',
    url: api.config.companyAPI + '/coupon/v1/demand',
    data: params
  });

//식권 신청 승인
export const ApproveMeal = (params) =>
  request({
    method: 'post',
    url: api.config.companyAPI + '/coupon/v1/demand',
    data: params
  });

//식대 지급/차감
export const SikdaeGiveDeduct = (params) =>
  request({
    method: 'post',
    url: api.config.companyAPI + '/coupon/v1/task',
    data: params
  });

//식대 지급/차감 > 엑셀파일 업로드 체크
export const SikdaeExcelCheck = (params) =>
  request({
    method: 'post',
    url: api.config.companyAPI + '/coupon/v1/task/upload/check',
    data: params,
    type: 'file'
  });

//식대 지급/차감 > 엑셀파일 업로드
export const SikdaeExcelUpload = (params) =>
  request({
    method: 'post',
    url: api.config.companyAPI + '/coupon/v1/task/upload',
    data: params,
    type: 'file'
  });

//가맹점 제한 조회
export const LimitStoreSel = (params) =>
  request({
    url: api.config.companyAPI + '/company/v1/group/' + params.groupidx + '/policy/' + params.policyidx + '/blacklist'
  });

//식대 지급/차갑 엑셀 파일 다운
export const ExcelDownload = (params) =>
  request({
    url: api.config.companyAPI + '/coupon/v1/task/upload/download?' + params
  });

//식대 지급/차감 엑셀 업로드 체크
export const ExcelUploadCheck = (params) =>
  request({
    method: 'post',
    url: api.config.companyAPI + '/coupon/v1/task/upload/check',
    data: params,
    type: 'file'
  });

//식대 지급/차감 엑셀 업로드
export const ExcelUpload = (params) =>
  request({
    method: 'post',
    url: api.config.companyAPI + '/coupon/v1/task/upload',
    data: params,
    type: 'file'
  });

//식대정책 변경 내역
export const ChangePolicyHistorySel = (params) =>
  request({
    url: api.config.companyAPI + '/history/v1/policy?' + params
  });

//권한 중에 schedule 권한이 있을 때 식대지급 start 시간 조회
export const ScheduleStartDate = (params) =>
  request({
    url: api.config.companyAPI + '/coupon/v1/task/schedule'
  });
