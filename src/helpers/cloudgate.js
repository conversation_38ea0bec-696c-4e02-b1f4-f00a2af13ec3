/**
 * 클라우드 게이트
 */
import storage from 'helpers/storage';

export const loadScript = async (isShowButton, params) => {
  const company = storage.get('company');
  const { signId, id, name = '로그인하지 않은 유저입니다.', cellphone = {} } = params;
  const { id: companyId, name: companyName } = company.info;

  Twc.Chat.init('init', {
    brandKey: 'nkoIrTyASTPY5Vb6ZhBAkQ',
    channelType: 'scenario',
    loginId: signId,
    userId: id,
    userName: name,
    userPhone: cellphone.value,
    companyId,
    companyName,
    userAgent: navigator.userAgent,
    buttonOption: {
      showLauncher: isShowButton,
      zIndex: 1,
      bottom: 25,
      right: 25
    }
  });
};

export const openChat = () => {
  Twc.Chat.open();
};
