import React from 'react';
import { dispatch } from 'react-redux';
import { bindActionCreators } from 'redux';
import PropTypes from 'prop-types';
import graylog from 'helpers/graylog';
import moment from 'moment-timezone';

import api from 'config';
import axios from 'axios';
import { removeUser } from 'helpers/datadog';
import storage from 'helpers/storage';
import toastr from 'helpers/toastr';
import * as etcAction from 'actions/etc';
import * as logoutAction from 'actions/company';
import menuList from 'config/menuList';
import { errorBind } from 'apis/errorHandler';
import errorHandler from 'apis/errorHandler';
import history from 'vendys/utils/history';
import store from './loggers';

moment.tz.setDefault('Asia/Seoul');
const authenticationUrl = api.config.authAPI + '/vendys/v1/token';

const grayLogCall = (pAuth, pInfo, pCompany, url, response, params) => {
  const auth = pAuth;
  const info = pInfo;
  const company = pCompany;
  let urlName = '';
  let path = window.location.pathname;

  let message = '';

  menuList.depth1.some((depth) => {
    if (depth.depth2.length > 0) {
      depth.depth2.some((data) => {
        if (data.url === path) {
          urlName = data.name;
          return true;
        }
      });
    } else {
      if (depth.url === path) {
        urlName = depth.name;
        return true;
      }
    }
  });

  const data = {
    appname: 'corp-web',
    message: '',
    log: {
      type: 'RESPONSE',
      url: path,
      urlName: urlName,
      apiUrl: url,
      accessToken: '',
      status: response.status,
      params: params,
      errorMessage: '',
      browser: navigator.userAgent
    }
  };

  if (response.status !== 200 && response.status !== 201) {
    data['log']['type'] = 'ERROR';
    data['log']['errorMessage'] = response.message;
  }

  //로그인 되어 있을 때
  if (company && company.user) {
    //회사 정보
    let companyName = company.user.company.name;
    let userName = company.user.name;
    let userID = company.userID;
    let accessToken = auth.aToken;

    data['message'] = companyName + ' / ' + userName + ' / ' + userID;
    data['log']['accessToken'] = accessToken;
    data['log']['auth'] = info.auth;

    //처음 로그인 할 떄
  } else {
    //인증 호출
    // if(url === api.config.authAPI + "/oauth2/token"){
    if (url === authenticationUrl) {
      if (response && response.data) {
        const { accessToken } = response.data;
        data['message'] = '로그인 / ' + accessToken;
      } else {
        data['message'] = '로그인 / error : ' + response.error.message;
      }
    } else {
      data['message'] = '최소 me호출';
    }
  }

  graylog({
    data: data
  });
};

export const logout = async (callback = null) => {
  try {
    const token = storage.get('auth');
    await store.dispatch(logoutAction.LogoutStepTwo({ token: token.aToken }));
  } catch (e) {
    const { response } = e;
    if (response.data) {
      toastr('top-right', 'warning', response.data.message);
    }
  } finally {
    console.log('logout !!!! session clear', callback);
    if (typeof callback === 'function') {
      callback();
    } else if (typeof callback === 'string' && callback === 'error') {
      storage.clearLogout();
      removeUser();
      history.push('/', { from: { pathname: window.location.pathname } });
    } else {
      storage.clearLogout();
      removeUser();
      window.location.href = '/';
    }
  }
};

const request = ({ url, method = 'get', params, data, type, config }) => {
  const auth = storage.get('auth');
  const info = storage.get('info');
  const company = storage.get('company');
  const isAuth = url.split('/');
  const env = api.config.processEnv;

  if (url.indexOf(api.config.companyAPI) > -1 && !auth) {
    return;
  }
  const header = () => {
    //인증 API를 제회안 모든 API 호출 시
    if (url.indexOf(api.config.authAPI) < 0) {
      let isMe = false;

      if (
        url === api.config.companyAPI + '/addons/v1/me' ||
        url.search(`${api.config.companyAPI}/captain-payment/auth/v1/`) > -1
        || (url === api.config.companyAPI + '/captain-payment/pass/v1/me' && !company || !company.user || !company.id || !company.user.id)
        || url === api.config.companyAPI + '/captain-payment/pass/v1/service/sikdea'
      ) {
        isMe = true;
      }
      /*
        me() API hearder : token, userid,
        me() API를 제외한 모든 API : token, userid, comid
      */
      let headerInfo = isMe
        ? {
            Authorization: 'Bearer ' + auth.aToken
          }
        : {
            Authorization: 'Bearer ' + auth.aToken,
            'x-userid': company.user.id,
            'x-comid': company.id,
            'x-corp': JSON.stringify(info.auth),
            'content-type': type !== 'file' ? 'application/json;charset=UTF-8' : 'multipart/form-data'
            // "x-userid"  : company.userID,
          };

      return headerInfo;
    } else {
      //인증 호출(로그인, token 갱신)이 아닌 강제로그아웃일 때 header token 추가
      if (url !== authenticationUrl) {
        const headerInfo = { Authorization: 'Bearer ' + auth.aToken };
        return headerInfo;
      } else {
        storage.set(
          'time',
          moment()
            .add(50, 'm')
            .format('mm')
        );
        return { 'X-User-Agent': `Vendys/1.0 ${JSON.stringify({ client: 'SikdaeUser', os: 'WEB' })}` };
      }
    }
  };

  let isDuplPending = true;
  axios.interceptors.response.use(
    (response) => {
      // 응답 데이터를 가공
      // ...
      return response;
    },
    (error) => {
      // 오류 응답을 처리
      // ...
      const {
        response: { data: res }
      } = error;
      const { status, message: content, title } = res;
      if (res && errorBind(status)) {
        if (isDuplPending) {
          isDuplPending = false;

          const { close, title: _title, stopBubble} = errorBind(status);
          if(stopBubble) {
            return errorHandler(error)
          }
          const modal = {
            close,
            open: true,
            title: _title || title,
            content
          };
          store.dispatch(etcAction.RootCommonModalAction(modal));
        }
        return false;
      }
      return Promise.reject(error);
    }
  );

  return axios({
    method,
    url,
    data,
    params,
    responseType: type === 'excel' ? 'arraybuffer' : null,
    headers: header(),
    ...config
  })
    .then((response) => {
      //graylog 호출
      try {
        if (env === 'production-test') {
          // grayLogCall(auth, info, company, url, response, params);
        }
      } catch (e) {
        console.log('###### e : ', e);
      }
      return response;
    })
    .catch((error) => {
      const response = error.response.data;
      const { status } = response;
      if (status === -2302 || status === -2303 || status === -2304) {
        console.log('### : 인증 토큰 폐기');
        toastr('top-right', 'error', response.message);
        logout();
        setTimeout(() => {}, 1500);
        return;
      } else if (status === -2306) {
        console.log('### : 인증 만료');
        toastr('top-right', 'error', response.message);
        setTimeout(() => {
          logout();
        }, 1500);
      } else if (status === -2400 || status === 500) {
        toastr('top-right', 'error', response.message);
        setTimeout(() => {
          if (status === -2400) {
            logout();
          }
        }, 1500);
      }

      try {
        if (env === 'production') {
          // grayLogCall(auth, info, company, url, response, params);
        }
      } catch (e) {
        console.log('###### e : ', e);
      } finally {
        throw error;
      }
    });
};

export default request;
