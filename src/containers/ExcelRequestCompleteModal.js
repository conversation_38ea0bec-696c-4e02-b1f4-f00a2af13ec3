import React from 'react';
import { Button, Modal } from 'semantic-ui-react';

const ExcelRequestCompleteModal = ({ open, close, onExcelDownList }) => {
  return (
    <Modal open={open} onClose={close} style={{ width: 510 }}>
      <Modal.Header style={{ border: 'none', padding: 20 }}>
        <div style={{ width: 500 }}>엑셀 출력 요청 완료</div>
      </Modal.Header>
      <div style={{ padding: '0px 20px 20px', fontSize: 16, lineHeight: '24px', color: '#747474' }}>
        <div>엑셀 출력 요청이 완료되었습니다. </div>
        <div>파일 생성 후 “엑셀 다운로드 내역“에서 다운로드 받으실 수 있지만,</div>
        <div>
          데이터가 많은 경우 엑셀 출력에 시간이 소요되어 “엑셀 다운로드 내역“에서 내역이 보이지 않을 수 있습니다.
        </div>
        <div>5분 후에도 생성이 되지 않았다면 담당자에게 문의 주세요.</div>
        <div style={{ paddingTop: 20, textAlign: 'end' }}>
          <Button
            color="black"
            inverted
            onClick={close}
            style={{
              marginRight: 10,
              boxShadow: 'none !important'
            }}
          >
            확인
          </Button>
          <Button onClick={onExcelDownList} color="green">
            엑셀 다운로드 내역 이동
          </Button>
        </div>
      </div>
    </Modal>
  );
};
export default ExcelRequestCompleteModal;
