import { COMPANY_API } from 'apis';

export const getAddressBook = async (params)=>{
  var result = await COMPANY_API.get('/captain-payment/quick/v1/booking/address-book', {
    params
  });

  result.data['data'] = result.data['addressBookList']
  delete result.data['addressBookList'];
  return result.data;
}

export const delAddressBook = async (addressBookIdx)=>{
  var result = await COMPANY_API.delete(`/captain-payment/quick/v1/booking/address-book/${addressBookIdx}`, {  });
  return result.data;
}


