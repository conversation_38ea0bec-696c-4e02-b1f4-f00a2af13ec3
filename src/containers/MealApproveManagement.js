import React, { Component, Fragment } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as cm_action from 'actions/commons';
import * as sik_action from 'actions/sikdae';
import storage from 'helpers/storage';
import cm from 'helpers/commons';
import update from 'react-addons-update';
import { MealApproveAccepterOptions, MealApproveAccepterResult } from 'components/MealApproveList';
import AddAccepterModal from 'components/MealApproveList/Modal/AddAccepterModal';
import { Modal, Grid, Form, Button } from 'semantic-ui-react';
import toastr from 'helpers/toastr';

class MealApproveManagement extends Component {
  state = {
    isBlock: true,
    accepterTemp: null,
    openModal: false,
    authApproveInfo: null,
    acceptersMap: null,
    accepter: null,
    checkType: null,
    checkedId: [],
    temp: null,
    searchKeyword: null
  };

  componentWillReceiveProps(nextProps) {
    const nApproveAccepterList = nextProps.approveAccepterList;

    if (nApproveAccepterList) {
      const { authApproveInfo } = this.state;
      const { demandIdx } = authApproveInfo;
    }
  }

  componentWillMount() {
    this.setApproveData();
  }

  componentWillUnmount() {
    storage.remove('authApproveInfo');
  }

  setApproveData = async () => {
    const newState = storage.get('authApproveInfo');
    await this.setState({ authApproveInfo: newState });

    const { authApproveInfo } = this.state;
    const { demandIdx, accepter } = authApproveInfo;

    if (accepter == 'ALL') {
      this.setState({
        accepterTemp: { data: { accepters: [] } }
      });
    } else {
      this.accepterSel(demandIdx);
    }
    this.staffSel();
    this.setState({ accepter });
    cm.mainTitle('식권신청 > 승인권한 관리하기');
  };

  // 승인자 조회
  accepterSel = async (demandIdx, params) => {
    const { sikAPI } = this.props;
    await sikAPI.mealApprovalAccepterList(demandIdx, params);
    const { approveAccepterList } = this.props;
    if (approveAccepterList) {
      const { accepters } = approveAccepterList.data;
      const acceptersMap = cm.excludeUseMap(accepters);
      this.setState({
        accepterTemp: approveAccepterList,
        acceptersMap: acceptersMap
      });
    }
  };

  //사용자 조회
  staffSel = async (params) => {
    const { cmAPI } = this.props;

    try {
      await cmAPI.staffSel(params);
    } catch (e) {
      const response = e.response;
      if (response) {
        toastr('top-right', 'error', response.data.message);
      }
      console.log('###### staffSel error : ', e);
    }
  };

  // 사용자 추가
  acceptersAdd = (checkedData, type) => {
    const { accepterTemp } = this.state;
    let temp = accepterTemp.data.accepters;
    temp = temp.concat(checkedData);

    const accepters = update(this.state.accepterTemp, {
      data: { accepters: { $set: temp } }
    });

    const acceptersMap = cm.excludeUseMap(temp);

    this.setState({
      checkType: type,
      acceptersMap: acceptersMap,
      accepterTemp: accepters
    });
  };

  // 사용자 삭제
  accepterRemove = (id) => {
    const { checkedId, accepterTemp } = this.state;

    if (accepterTemp) {
      const { accepters } = accepterTemp.data;
      const userId = cm.removeDup(JSON.parse(JSON.stringify(accepters)), checkedId);

      const acceptersMap = cm.excludeUseMap(userId);
      this.setState({
        accepterTemp: update(this.state.accepterTemp, {
          data: { accepters: { $set: userId } }
        }),
        acceptersMap: acceptersMap,
        checkedId: []
      });
    }
  };

  checkAccepterId = (data, type) => {
    this.setState({
      checkedId: data,
      checkType: type
    });
  };

  // 수정 완료
  editConfirm = async () => {
    const { accepter, accepterTemp, authApproveInfo, type } = this.state;
    const { demandIdx, groupIdx, policyIdx } = authApproveInfo;
    const { accepters } = accepterTemp.data;

    if (accepter === 'ALL') {
      const { staffList, sikAPI } = this.props;

      if (staffList && staffList.data) {
        const { user } = staffList.data;
        let userId = [];

        user.forEach((uItem) => {
          const { level } = uItem;

          if (level !== -5 || level !== -1 || level !== 0) {
            userId.push(uItem.id);
          }
        });

        if (userId.length !== 0) {
          const params = {
            accepter: 'ALL',
            userIdList: userId
          };

          await sikAPI.mealAccepterMod(demandIdx, params);
          storage.set('beforeAccepter', {
            gIdx: groupIdx,
            pIdx: policyIdx
          });
          const { history } = this.props;
          history.push('/main/mealApproveList');
        } else {
          toastr('top-right', 'warning', '사용자를 추가해주세요.');
          return;
        }
      }
    } else if (accepterTemp) {
      const { sikAPI } = this.props;
      let userId = [];
      accepters.forEach((accepter) => userId.push(accepter.id));

      if (userId.length !== 0) {
        const params = {
          accepter: type === 'all' ? 'ALL' : accepter,
          userIdList: userId
        };

        await sikAPI.mealAccepterMod(demandIdx, params);
        storage.set('beforeAccepter', {
          gIdx: groupIdx,
          pIdx: policyIdx
        });
        const { history } = this.props;
        history.push('/main/mealApproveList');
      } else {
        toastr('top-right', 'warning', '사용자를 추가해주세요.');
        return;
      }
    }
  };

  // 수정 취소
  editCancel = () => {
    const { authApproveInfo } = this.state;
    const { groupIdx, policyIdx } = authApproveInfo;

    storage.set('beforeAccepter', {
      gIdx: groupIdx,
      pIdx: policyIdx
    });
    const { history } = this.props;
    history.push('/main/mealApproveList');
  };

  //반려 모달 열기
  openModal = () => {
    const { authApproveInfo } = this.state;
    const { demandIdx } = authApproveInfo;

    this.setState({ openModal: true });
    this.staffSel();
  };

  //반려 모달 닫기
  closeModal = () => this.setState({ openModal: false });

  handleChange = (value) => {
    this.setState({ accepter: value });
  };

  // ID, 이름 검색 텍스트 변경
  handleSubmit = async (value) => {
    await this.setState({
      searchKeyword: value
    });
  };

  render() {
    const { accepterTemp, authApproveInfo, acceptersMap, accepter, checkedId, searchKeyword } = this.state;
    const modalFn = {
      open: this.state.openModal,
      openModal: this.openModal,
      closeModal: this.closeModal
    };

    return (
      <main className="mam">
        <MealApproveAccepterOptions
          modalFn={modalFn}
          accepter={accepter}
          accepterTemp={accepterTemp}
          checkedId={checkedId}
          authApproveInfo={authApproveInfo}
          handleSubmit={this.handleSubmit}
          accepterSel={this.accepterSel}
          accepterRemove={this.accepterRemove}
          handleChange={this.handleChange}
          accepterSearch={this.accepterSearch}
        />
        <MealApproveAccepterResult
          checkInput
          searchKeyword={searchKeyword}
          isModal={false}
          accepter={accepter}
          ttt={this.state.temp}
          approveAccepterList={accepterTemp}
          checkAccepterId={this.checkAccepterId}
        />

        <Grid style={{ marginTop: 5, padding: 16, textAlign: 'right' }}>
          <Button inverted color="grey" onClick={this.editCancel} content="취소" />
          <Button positive content="수정완료" onClick={this.editConfirm} style={{ width: 200, marginLeft: 8 }} />
        </Grid>

        <AddAccepterModal
          modalFn={modalFn}
          staffSel={this.staffSel}
          acceptersAdd={this.acceptersAdd}
          acceptersMap={acceptersMap}
          authApproveInfo={authApproveInfo}
        />
      </main>
    );
  }
}

MealApproveManagement = connect(
  (state) => ({
    staffList: state.commons.staff,
    approveAccepterList: state.sikdae.approveAccepterMeal,
    mealAccepterMod: state.sikdae.mealAccepterMod
  }),
  (dispatch) => ({
    cmAPI: bindActionCreators({ staffSel: cm_action.StaffSel }, dispatch),
    sikAPI: bindActionCreators(
      {
        mealApprovalAccepterList: sik_action.MealApprovalAccepterList,
        mealAccepterMod: sik_action.MealAccepterMod
      },
      dispatch
    )
  })
)(MealApproveManagement);

export default MealApproveManagement;
