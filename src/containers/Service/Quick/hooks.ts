import { useQuery, useMutation, useQueryClient } from 'react-query';
import {
  getQuickOrderList,
  getQuickOrderDetail,
  cancelQuickOrder,
  checkQuickOrderPrice,
  saveQuickOrder,
  getQuickOrderStat,
  getAddressBookList,
  saveAddressBook,
  deleteAddressBook
} from 'apis/captainpayment/quick';

import { IQuickOrderList, IOrderAddressList, IQuickOrderAddress } from './types';

// 퀵 주문 목록 조회
export const useGetQuickOrderList = (params: unknown) => {
  return useQuery<IQuickOrderList>(['getQuickOrderList', params], () => getQuickOrderList(params));
};

// 엑셓용 퀵 주문 목록 조회
export const useGetQuickOrderExcelList = () => {
  return useMutation(['getQuickOrderList'], (params: unknown) => getQuickOrderList(params));
};

// 퀵 주문 상세 조회
export const useGetQuickOrderDetail = () => {
  return useMutation(['getQuickOrderDetail'], (quickIdx: number) => getQuickOrderDetail(quickIdx));
};

// 퀵 배송 주문 취소
export const useCancelQuickOrder = () => {
  const queryClient = useQueryClient();
  return useMutation(['cancelQuickOrder'], (quickIdx: number) => cancelQuickOrder(quickIdx), {
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ['getQuickOrderList'] })
  });
};

// 퀵 배송 예상 가격 조회
export const useCheckQuickOrderPrice = () => {
  return useMutation(['checkQuickOrderPrice'], (params: unknown) => checkQuickOrderPrice(params));
};

// 퀵 배송 주문 생성
export const useSaveQuickOrder = () => {
  return useMutation(['saveQuickOrder'], (params: unknown) => saveQuickOrder(params));
};

// 퀵 서비스 통계 조회
export const useGetQuickOrderStat = (params: unknown) => {
  return useQuery(['getQuickOrderStat', params], () => getQuickOrderStat(params));
};

// 주소록 목록 조회
export const useAddressBookList = (isAll: boolean) => {
  return useQuery<IOrderAddressList>(['getAddressBookList'], () => getAddressBookList({ isAll }));
};

// 주소록 항목 추가
export const useSaveAddressBook = () => {
  const queryClient = useQueryClient();
  return useMutation(['saveAddressBook'], (params: IQuickOrderAddress) => saveAddressBook(params), {
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ['getAddressBookList'] })
  });
};

// 주소록 항목 삭제
export const useDeleteAddressBook = () => {
  const queryClient = useQueryClient();

  return useMutation(['deleteAddressBook'], (addressIdx: number) => deleteAddressBook(addressIdx), {
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ['getAddressBookList'] })
  });
};
