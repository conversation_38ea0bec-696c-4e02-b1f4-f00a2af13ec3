import React, { ReactElement, useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import useFetch from 'use-http';

import api from 'config';
import { FormattedMessage } from 'react-intl';

import messages from './messages';
import { columns } from './viewMeta';
import { columns } from './constants';
import { Container } from './styles';
import { addTodo, deleteTodo, toggleTodo } from './actionCreators'
import { defaultAction } from './actions'


interface QuickGuidanceProps {
    
}

 function QuickGuidance({}: QuickGuidanceProps): ReactElement {
    const [todos, setTodos] = useState(undefined)
    const { get, post, response, loading, error } = useFetch(`${api.config.companyAPI}`)
  
    useEffect(() => {
        initialData()
    }, [])

    async function initialData() {
        const initialTodos = await get('/stat/v1/company/division/top')
        if (response.ok) {
            var data = await response.json()
            setTodos(data)
        }
    }
    
    return (
        <Container>
          <div><FormattedMessage {...messages.header} /></div>
            {todos && JSON.stringify(todos)}
        </Container>
    )
}

const mapStateToProps = (state) => {
    // return { todos: state.todos };
}

const mapDispatchToProps = (dispatch, ownProps) => {
    // const boundActions = bindActionCreators({ defaultAction }, dispatch)
  return {
    // dispatchPlainObject: () => dispatch({ type: 'MY_ACTION' }),
    // dispatchActionCreatedByActionCreator: () => dispatch(createMyAction()),
    // ...boundActions,
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(QuickGuidance)