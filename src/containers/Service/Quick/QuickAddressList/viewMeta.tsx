import React from 'react';
import moment from 'moment';
import { Button } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';

// eslint-disable-next-line import/prefer-default-export
export const ADDREESS_COLUMNS = [
  { Header: '주소 별명', accessor: 'addressBookName' },
  { Header: '주소', accessor: 'roadAddress', width: 200 },
  { Header: '주소 상세', accessor: 'addressDetail', width: 200 },
  { Header: '이름', accessor: 'name' },
  { Header: '연락처', accessor: 'cellphone' },
  { Header: '최근 수정 사용자', accessor: 'updateUser' },
  {
    Header: '최근 수정 일시',
    accessor: 'updateDate',
    Cell: ({ row: { original } }) => {
      return moment(original?.updateDate).format('YYYY-MM-DD HH:mm:ss');
    }
  },
  {
    HeaderId: '삭제',
    accessor: 'addressBookIdx',
    width: 60,
    Cell: ({ row: { original }, onDeleteAddressBook }) => {
      return (
        <Button
          type="primary"
          icon={<DeleteOutlined />}
          onClick={() => onDeleteAddressBook(original?.addressBookIdx)}
        />
      );
    }
  }
];
