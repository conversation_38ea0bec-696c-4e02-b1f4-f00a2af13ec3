import React from 'react';
import moment from 'moment';

import { QUICK_DISPLAY_STATUS } from '../types';

const defaultOptions = { type: 'string', viewProps: { disabled: true } };

export const QUICK_ORDER_DEFAULT_COLUMN = [
  { label: '예약 번호', name: 'orderId', ...defaultOptions },
  { label: '예약자', name: 'userName', ...defaultOptions },
  { label: '예약 일시', name: 'createDate', ...defaultOptions }
];

const DISPLAY_STATUS_DESCRIPTION = {
  ORDER_RECEIVED: [
    '배송이 접수 되었습니다. 배차 완료 전까지 수정 및 취소 가능합니다.',
    '발송인에게 접수 정보를 알림톡으로 발송하였습니다.'
  ],
  DRIVER_ASSIGNED: ['배차가 완료되었습니다.', '배차완료 후 해당 주문건은 취소 할 수 없습니다.'],
  PICKUP_COMPLETE: [
    '배송기사님이 배송할 물품을 픽업하였습니다.',
    '발송인, 수령인에게 배송 정보를 알림톡으로 발송하였습니다.'
  ],
  DELIVERY_COMPLETE: ['배송이 완료하였습니다.', '발송인, 수령인에게 배송 완료 정보를 알림톡으로 발송하였습니다.'],
  ORDER_CANCELLED: ['배송접수가 취소되었습니다.', '발송인에게 접수 취소정보를 알림톡으로 발송하였습니다. ']
};

export const QUICK_ORDER_STATUS_COLUMN = [
  {
    Header: '상태',
    Cell: ({ row: { original } }) => {
      return QUICK_DISPLAY_STATUS[original?.status] ?? '-';
    }
  },
  {
    Header: '상태 변경 일시',
    Cell: ({ row: { original } }) => {
      return moment(original?.updatedAt).format('YYYY-MM-DD HH:mm:ss');
    }
  },
  {
    Header: '설명',
    Cell: ({ row: { original } }) => {
      return (
        <ul style={{ paddingLeft: '12px' }}>
          {DISPLAY_STATUS_DESCRIPTION[original?.status].map((content) => (
            <li key={content} style={{ listStyleType: 'disc' }}>
              {content}
            </li>
          ))}
        </ul>
      );
    }
  }
];

export const QUICK_ORDER_DETAIL_COLUMN = [
  { label: '물품 정보', name: 'quickGoodsType', ...defaultOptions },
  { label: '차량 종류', name: 'vehicle', ...defaultOptions },
  { label: '보내는 분', name: 'quickAddressList', id: 'pickup', ...defaultOptions },
  { label: '받는 분', name: 'quickAddressList', id: 'dropoff', ...defaultOptions },
  { label: '픽업 시간', name: 'appointmentDate', ...defaultOptions },
  { label: '상품', name: 'orderType', ...defaultOptions },
  { label: '물품명', name: 'goodsInfo', ...defaultOptions },
  { label: '배송 메모', name: 'remark', ...defaultOptions }
];

export const QUICK_WAYPOINT_COLUMN = { label: '경유지', name: 'quickAddressList', id: 'waypoint', ...defaultOptions };
