import React from 'react';
import { Switch, Route } from 'react-router-dom';
import QuickReservationDetail from './QuickReservationDetail';
import QuickReservation from './QuickReservation';

const Quick = ({ match, location, permission, ...o }) => {
  return (
    <Switch location={location}>
      <Route exact path={match.path} render={({ history }) => <QuickReservation history={history} />} />
      <Route
        exact
        path={`${match.path}/item/:id`}
        render={({ history }) => <QuickReservationDetail history={history} />}
      />
      <Route exact path={`${match.path}/:id`} render={(props) => <QuickReservation {...props} />} />
    </Switch>
  );
};

export default Quick;
