import React, { ReactElement, useState } from 'react';
import styled from 'styled-components';
import moment from 'moment';
import { connect } from 'react-redux';
import { Modal, Spin, Button } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { withModalContext } from 'vendys/context/ModalContext';
import { PageContent } from 'styles/global';

import OptionBar from 'vcomponents/OptionBar';
import Table from 'vcomponents/Table';
import ViewPanel from 'vcomponents/Form/ViewPanel';

import quickExcelJson from './excelJsonFile.json';

import { optionbarTemplate, quickOrderListColumns } from './viewMeta';
import { useGetQuickOrderList, useGetQuickOrderExcelList } from '../hooks';
import { IQuickOrder, QUICK_DISPLAY_STATUS, GOODS_SIZE_CATEGORIES, QUICK_TYPE } from '../types';

const { confirm } = Modal;

export const QuickListWrapper = styled(Spin)``;

interface QuickReservationListProps {
  meInfo: any;
  context: any;
  history: any;
}

function QuickReservationList({ meInfo, context, history }: QuickReservationListProps): ReactElement {
  const [params, setParams] = useState({
    startDate: moment()
      .add(-1, 'week')
      .format('YYYY-MM-DD'),
    endDate: moment().format('YYYY-MM-DD'),
    status: null,
    page: 0
  });
  const { data: quickList, isLoading } = useGetQuickOrderList(params);
  const { mutateAsync: getQuickOrderExcelList } = useGetQuickOrderExcelList();

  const addExcelFile = async (excelData: IQuickOrder[]) => {
    const fileName = `퀵 예약 및 내역_${moment().valueOf()}.xlsx`;
    const newRows = excelData.map((item) => {
      const startPoint = item.quickAddressList?.find(({ addressType }) => addressType === 'STARTPOINT');
      const endPoint = item.quickAddressList?.find(({ addressType }) => addressType === 'ENDPOINT');
      return [
        item.orderId,
        item.userName,
        startPoint.name,
        endPoint.name,
        QUICK_TYPE[item.orderType] ?? '-',
        GOODS_SIZE_CATEGORIES[item.quickGoodsType] ?? '-',
        item.goodsCnt,
        item.goodsInfo,
        moment(item.createDate).format('YYYY-MM-DD HH:mm'),
        moment(item.appointmentDate).format('YYYY-MM-DD HH:mm'),
        item.amount,
        item.vatAmount,
        item.totalAmount,
        QUICK_DISPLAY_STATUS[item.displayStatus]
      ];
    });
    const apiExcelDatas = { 0: { startRowNum: 2, newRows } };
    // @ts-ignore
    window.handleFileExport(quickExcelJson, apiExcelDatas, fileName);
  };

  const handleClickExcelDown = async (page: number, excelData?: IQuickOrder[]) => {
    const size = 2000;
    const startDate = moment()
      .add(-3, 'month')
      .format('YYYY-MM-DD');
    const endDate = moment().format('YYYY-MM-DD');
    const quickData = await getQuickOrderExcelList({ page, size, startDate, endDate });

    const array = excelData.concat(quickData.content || []);
    if (quickData.totalPages > quickData.number + 1) {
      handleClickExcelDown(page + 1, array);
    } else {
      addExcelFile(array);
    }
  };

  const showConfirm = () => {
    const content = `현재 퀵대장 서비스가 고객사의 요청으로 일시중지 상태입니다. 일시 중지 상태일 경우, 생성, 수정, 삭제와 같은 활동은 제한됩니다.`;
    confirm({
      title: '에러',
      icon: <ExclamationCircleOutlined />,
      content,
      cancelButtonProps: { style: { display: 'none' } }
    });
  };

  const handleSearch = async (value) => {
    const { date, searchType, searchValue, displayStatus } = value;

    const startDate = moment(date.startDate).format('YYYY-MM-DD');
    const endDate = moment(date.endDate).format('YYYY-MM-DD');
    const status = displayStatus !== 'ALL' ? displayStatus : null;

    const field = searchType === 'ORDERID' ? 'orderId' : 'name';
    setParams({ [field]: searchValue, startDate, endDate, status, page: 0 });
  };

  const handleRowClick = async (rowData: IQuickOrder) => {
    history.push('/service/quick/reservation/item', { quickIdx: rowData.quickManagerIdx });
  };

  const handlePageChange = (pageInfo: Record<string, number>) => {
    setParams((prevState) => ({ ...prevState, page: pageInfo.page - 1 }));
  };

  const handleAddressManagement = () => history?.push('/service/quick/address');
  const handleQuickReservation = () => {
    const { serviceList } = meInfo?.companyInfo;
    const service = serviceList?.find(({ serviceType }) => serviceType === 'QUICK');
    if (!service || service.status === 'INACTIVE') {
      showConfirm();
    } else {
      history.push('/service/quick/reservation/booking');
    }
  };

  const buttons = [
    <Button onClick={handleAddressManagement}>주소 관리</Button>,
    <Button onClick={handleQuickReservation}>퀵 예약하기</Button>,
    <Button onClick={() => handleClickExcelDown(0, [])}>엑셀 다운로드</Button>
  ];
  return (
    <QuickListWrapper spinning={isLoading}>
      <ViewPanel title="예약 및 내역" buttons={buttons}>
        <OptionBar colInRow={4} template={optionbarTemplate} onClick={handleSearch} />
        <PageContent>
          <Table
            columns={quickOrderListColumns}
            data={((quickList?.content as unknown) as []) || []}
            pageInfo={{
              page: quickList?.number ?? 0,
              pageRow: quickList?.size ?? 15,
              totalCount: quickList?.totalElements ?? 0
            }}
            onRowClick={handleRowClick}
            onChange={handlePageChange}
            cellProps={{ context }}
          />
        </PageContent>
      </ViewPanel>
    </QuickListWrapper>
  );
}

const mapStateToProps = (state) => ({
  meInfo: state.auth.captainPayment
});

export default withModalContext(connect(mapStateToProps)(QuickReservationList));
