import React, { ReactElement, useState, useEffect, useContext } from 'react';
import { FormattedMessage } from 'react-intl';
import { Row, Checkbox, Button, Steps, Select, Radio } from 'antd';
import 'antd-mobile/dist/antd-mobile.css';
import useFetch from 'use-http';
import api from 'config';
import { InfoCircleOutlined } from '@ant-design/icons';
import { withModalContext } from 'context/ModalContext';
import { connect } from 'react-redux';
import ViewPanel from 'vcomponents/Form/ViewPanel';
import { AddressComponent } from './AddressPost/AddressComponent';
import { Container, Modals, NextButton, AddressModals } from '../styles';
import { params, defaultData } from '../viewMeta';
import messages from '../messages';
import { Context } from '../reducers/context';
import { Description } from './index';
const { Option } = Select;
const { Step } = Steps;

function QuickMobileAddress(props): ReactElement {
  const { history } = props;
  const { location } = history;
  const { get, post, response, loading, error } = useFetch(`${api.config.companyAPI}`);
  const {
    state: { addressStart, addressWay, addressEnd, existMid, isModal },
    dispatch
  } = useContext(Context);
  const [isModalVisible, setIsModalVisible] = useState(isModal);
  const [exist, setExist] = useState(existMid);
  const [addressStartData, setAddressStartData] = useState(addressStart);
  const [addressWayData, setAddressWayData] = useState(addressWay);
  const [addressEndData, setAddressEndData] = useState(addressEnd);
  const [idx, setIdx] = useState(undefined);
  const [addressType, setAddressType] = useState('STARTPOINT');
  const [description, setDescription] = useState(' ');
  const [form, setForm] = useState(undefined);
  const [addressBookList, setAddressBookList] = useState([]);
  const [addressBookRadio, setAddressBookRadio] = useState('NONE');
  const [disabled, setDisabled] = useState(true);
  const [addressBookModal, setAddressBookModal] = useState(false);

  useEffect(() => {
    const type = {
      bookingstartpoint: 'STARTPOINT',
      bookingwaypoint: 'WAYPOINT',
      bookingendpoint: 'ENDPOINT'
    };

    const descriptionId = {
      bookingstartpoint: 'start.address.required',
      bookingwaypoint: 'way.address.required',
      bookingendpoint: 'end.address.required'
    };
    const idx = location.pathname.split('/').reverse()[0];
    let addressTypes;
    if (Number(idx)) {
      setIdx(idx);
      const t = location.pathname.split('/').reverse()[1];
      addressTypes = type[t];
      setDescription(descriptionId[t]);
    } else {
      addressTypes = type[idx];
      setDescription(descriptionId[idx]);
    }
    setAddressType(addressTypes);
    getAddressBook();
    addressChange(
      addressTypes === 'STARTPOINT' ? addressStartData : addressTypes === 'WAYPOINT' ? addressWayData : addressEndData
    );
  }, []);

  const existMidChange = () => {
    dispatch({ type: 'EXISTMID', payload: !exist });
    setExist(!exist);
  };

  const getAddressBook = async () => {
    const address = await get(
      `/captain-payment/quick/v1/booking/address-book?${Object.entries(params)
        .map((e) => e.join('='))
        .join('&')}`
    );
    if (response.ok) setAddressBookList(address.addressBookList);
  };

  const addressChange = async (v) => {
    if (v) {
      v.type === 'STARTPOINT' && setAddressStartData({ ...addressStartData, ...v, userSelectedType: 'J' });
      v.type === 'WAYPOINT' && setAddressWayData({ ...addressWayData, ...v, userSelectedType: 'J' });
      v.type === 'ENDPOINT' && setAddressEndData({ ...addressEndData, ...v, userSelectedType: 'J' });

      if (!v.addressDetail || !v.roadAddress || !v.jibunAddress || !v.name || !v.cellphone) {
        setDisabled(true);
      } else {
        if (v.isSaveAddress && !v.addressBookName) {
          setDisabled(true);
        } else {
          setDisabled(false);
        }
      }
      dispatch({ type: v.type, payload: { ...v, userSelectedType: 'J' } });
    }
  };

  const handleOk = () => {
    setIsModalVisible(!isModalVisible);
    dispatch({ type: 'ISMODAL', payload: !isModalVisible });
  };

  const addressBookHandleOk = () => {
    setAddressBookModal(!addressBookModal);
  };

  const pNext = async () => {
    let url;
    const id = location.pathname.split('/').reverse()[0];
    if (addressType === 'STARTPOINT') {
      url = exist
        ? '/mobile/service/quick/reservation/bookingwaypoint'
        : '/mobile/service/quick/reservation/bookingendpoint';
    } else if (addressType === 'WAYPOINT') {
      url = '/mobile/service/quick/reservation/bookingendpoint';
    } else if (addressType === 'ENDPOINT') {
      url = '/mobile/service/quick/reservation/bookingpickup';
    }
    url = isNaN(id) ? url : `${url}/${idx}`;
    history.push(url);
  };

  const addressBookChange = (k) => {
    if (k === 'NONE') {
      addressChange({ ...defaultData, type: addressType });
    } else {
      const v = addressBookList.filter(({ addressBookIdx }) => k === addressBookIdx)[0];
      const a = { addressBookName: '', isSaveAddress: false };
      const target = { ...v, ...a };
      addressChange({ ...target, type: addressType });
    }
    setAddressBookRadio(k);
    setAddressBookModal(!addressBookModal);
  };

  let address;
  let current = 0;
  if (addressType === 'STARTPOINT') {
    current = 1;
    address = addressStartData;
  } else if (addressType === 'WAYPOINT') {
    current = 2;
    address = addressWayData;
  } else if (addressType === 'ENDPOINT') {
    current = 3;
    address = addressEndData;
  }

  return (
    <Container style={{ minWidth: '320px', height: 'max-content' }}>
      <ViewPanel title=" " style={{ height: '100%' }}>
        <Description id={description} onClick={handleOk} icon={addressType === 'STARTPOINT'} current={current} />
        <ViewPanel title=" " layout="vertical" theme="none" style={{ padding: 0 }}>
          <Row>
            <AddressComponent
              type={addressType}
              setForm={setForm}
              onChange={addressChange}
              onClick={addressBookHandleOk}
              address={address}
              form={form}
            />
            {addressType === 'STARTPOINT' && (
              <Checkbox onChange={existMidChange} style={{ marginLeft: 12 }} checked={exist}>
                <FormattedMessage id="quick.exist.mid" />
              </Checkbox>
            )}
          </Row>
        </ViewPanel>
        <NextButton type="primary" onClick={pNext} disabled={disabled}>
          다음
        </NextButton>
      </ViewPanel>

      <AddressModals
        visible={addressBookModal}
        onOk={addressBookHandleOk}
        onCancel={addressBookHandleOk}
        closable={false}
        footer={[]}
        bodyStyle={{ overflowY: 'auto', height: 300 }}
      >
        <div key="NONE" className="row" onClick={() => addressBookChange('NONE')}>
          새로운 주소입력
          <Radio value="NONE" checked={addressBookRadio === 'NONE'} onChange={(e) => addressBookChange('NONE')} />
        </div>
        {addressBookList.map((row) => (
          <div key={row.addressBookIdx} className="row" onClick={() => addressBookChange(row.addressBookIdx)}>
            {row.addressBookName}
            <Radio
              value={row.addressBookIdx}
              checked={addressBookRadio === row.addressBookIdx}
              onChange={(e) => addressBookChange(e.target.value)}
            />
          </div>
        ))}
      </AddressModals>

      {addressType === 'STARTPOINT' && (
        <Modals
          title={<FormattedMessage id={'quick.address.info'} />}
          visible={isModalVisible}
          onOk={handleOk}
          onCancel={handleOk}
          footer={[]}
        >
          <div>
            <div className="paddingBottom">
              <FormattedMessage {...messages.address_title} />
            </div>
            <div className="colorRed">
              <FormattedMessage {...messages.address_warning1} />
            </div>
            <div className="colorRed">
              <FormattedMessage {...messages.address_warning2} />
            </div>
          </div>
        </Modals>
      )}
    </Container>
  );
}

export default withModalContext(connect()(QuickMobileAddress));
