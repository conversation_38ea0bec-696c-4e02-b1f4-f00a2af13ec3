import { defineMessages } from 'react-intl';

export const scope = 'app.containers.QuickReservation';

export default defineMessages({
  expla1: {
    id: `${scope}.address.title`,
    defaultMessage: '모든 필수 정보 설정 후 예상 운임을 확인해주세요.'
  },

  expla2: {
    id: `${scope}.address.title`,
    defaultMessage: '예상운임 확인 후, 반드시 약관 동의 후 주문 완료해주세요.'
  },

  address_title: {
    id: `${scope}.address.title`,
    defaultMessage: '보내는 분과 받는 분의 주소를 설정해 주세요. 경유지가 있는 경우, 1개까지 추가 가능합니다.'
  },

  address_warning1: {
    id: `${scope}.address.warning`,
    defaultMessage:
      '(!) 현재 퀵 서비스는 수도권 지역 (서울, 경기, 인천)에만 적용되며, 점차 서비스 지역을 확대해 나갈 예정입니다.'
  },

  address_warning2: {
    id: `${scope}.address.warning`,
    defaultMessage:
      '(!) 보내는 분은 휴대전화번호를 입력해주세요. 예약하신 퀵 서비스의 상태를 문자로 확인 할 수 있습니다.'
  }
});
