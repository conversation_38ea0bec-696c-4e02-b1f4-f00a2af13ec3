import moment, { Moment } from 'moment';

import locale from 'antd/es/date-picker/locale/ko_KR';

export const mainDescription = `<div>모든 필수 정보 설정 후, 예상 운임을 확인해주세요.<br>예상 운임 확인 후, 반드시 약관 동의 후 주문 완료해주세요.</div>`;

export const goodDescription = `<div>운송해야할 물품의 정보를 입력해 주세요.</div>`;

export const addressDescription = `
<div>보내는 분과 받는 분의 주소를 설정해 주세요.</div>
<div class="warning">(!) 현재 퀵 서비스는 수도권 지역 (서울, 경기, 인천)에만 적용되며, 점차 서비스 지역을 확대하 나갈 예정입니다.</div>
<div class="warning">(!) 보내는 분은 휴대전화번호를 입력해주세요. 예약하신 퀵 서비스의 상태를 문자로 확인 할 수 있습니다.</div>
`;

export const etcDescription = `
<div class="warning">(!) 배송 당시, 기상 악화가 있을 경우, 운임이 조정될 수 있습니다.</div>
<div class="warning">(!) 배송 과정에서 다양한 사유로 회차 발생시, 확정운임이 변경될 수 있습니다.</div>
<div class="warning">(!) 확정 운임의 변경과 관련된 문의는 카카오T 고객센터(1599-9400) 문의해 주시길 바랍니다.</div>
`;

export const priceDescription = `
<div class="warning">(!) 배송 과정에서 다양한 이유로 운임이 변경될 수 있습니다.</div>
<div class="warning">(!) 운임이 변경될 경우, 카카오T 고객센터(1599-9400) 문의해 주시길 바랍니다.</div>
`;

export const disabledDate = (current: Moment | string) => {
  const maxDate = moment()
    .add(13, 'day')
    .endOf('day');
  const minDate = moment()
    .add(-1, 'day')
    .endOf('day');
  return (current && current < minDate) || current > maxDate;
};

export const disabledTime = (selectedDate: Moment | string) => {
  const currentDate = moment().add(30 - (moment().minute() % 10), 'minute');
  const isBefore = !currentDate.isBefore(selectedDate, 'day');
  return {
    disabledHours: () =>
      Array.from({ length: 24 }, (_, i) => i).filter((hour) => {
        return hour < currentDate.hour() && isBefore;
      }),
    disabledMinutes: (hour: number) =>
      Array.from({ length: 12 }, (_, i) => i * 5).filter((minute) => {
        if (hour === currentDate.hour() && isBefore) {
          return minute < currentDate.minute();
        }
        return false;
      })
  };
};

const defaultOptions = { type: 'string', rules: [{ required: true }] };
export const QUICK_GOODS_COLUMN: Record<string, any>[] = [
  {
    label: '물품 크기',
    type: 'EnumSelector',
    id: 'goodsType',
    name: 'goodsType',
    rules: [{ required: true }]
  },
  {
    label: '개수',
    id: 'goodsCnt',
    name: 'goodsCnt',
    help: '최대 10개까지 가능',
    type: 'InputNumber',
    viewProps: { min: 1, max: 10 },
    rules: [{ required: true }]
  },
  {
    label: '차량',
    id: 'vehicle',
    type: 'EnumSelector',
    viewProps: { enumId: 'ENUM_VEHICLE', placeholder: '차량을 선택해주세요.' }
  }
];

export const QUICK_ADDRESS_COLUMN: Record<string, any>[] = [
  {
    label: '주소록',
    name: 'addressBooks',
    id: 'addressBooks',
    type: 'string'
  },
  { label: '주소', id: 'addressSearchButton', name: 'address', ...defaultOptions },
  { label: ' ', id: 'addressDetail', name: 'addressDetail', type: 'string', viewProps: { placeholder: '상세 주소' } },
  { label: '이름', id: 'name', name: 'name', viewProps: { placeholder: '이름을 입력해 주세요' }, ...defaultOptions },
  {
    label: '연락처',
    id: 'cellphone',
    name: 'cellphone',
    viewProps: { placeholder: '연락처를 입력해 주세요' },
    ...defaultOptions
  }
];

export const SAVE_ADDRESS_COLOUMN: Record<string, any>[] = [
  {
    label: '주소록 별명',
    id: 'addressBookName',
    name: 'addressBookName',
    type: 'string',
    viewProps: { placeholder: '새로운 주소록에 등록 할 경우 입력해주세요.' }
  },
  {
    label: ' ',
    id: 'isSaveAddress',
    name: 'isSaveAddress',
    type: 'checkbox',
    viewProps: { defaultChecked: false, msgId: '입력한 주소를 주소록에 등록하겠습니다.' }
  }
];

export const QUICK_ETC_COLUMN: Record<string, any>[] = [
  {
    label: '픽업 시간',
    type: 'datetime',
    id: 'appointmentDate',
    name: 'appointmentDate',
    viewProps: {
      format: 'YYYY-MM-DD HH:mm',
      disabledDate,
      disabledTime,
      allowClear: false,
      minuteStep: 5,
      showTime: { format: 'HH:mm' },
      locale
    },
    rules: [{ required: true, message: 'quick.pickup.required' }]
  },
  {
    label: '퀵대장 상품',
    type: 'EnumRadio',
    viewProps: { enumId: 'ENUM_ORDER_TYPE' },
    id: 'orderType',
    name: 'orderType',
    rules: [{ required: true }]
  },
  {
    label: '물품명',
    id: 'goodsInfo',
    type: 'string',
    viewProps: { placeholder: '물품명을 입력해 주세요 (최대 20자 입력 가능)', maxLength: 20 }
  },
  {
    label: '배송 메모',
    id: 'remark',
    type: 'string',
    viewProps: { placeholder: '요청 사항을 입력해 주세요. (최대 70자 입력 가능)', maxLength: 70 }
  }
];
//

const defaultColumn = { type: 'string', viewProps: { disabled: true } };
export const QUICK_ORDER_DETAIL_COLUMN: Record<string, any>[] = [
  { label: '물품 정보', name: 'goodsType', ...defaultColumn },
  { label: '차량 종류', name: 'vehicle', ...defaultColumn },
  { label: '보내는 분', name: 'pickup', id: 'pickup', ...defaultColumn },
  { label: '받는 분', name: 'dropoff', id: 'dropoff', ...defaultColumn },
  { label: '픽업 시간', name: 'appointmentDate', ...defaultColumn },
  { label: '상품', name: 'orderType', ...defaultColumn },
  { label: '물품명', name: 'goodsInfo', ...defaultColumn },
  { label: '배송 메모', name: 'remark', ...defaultColumn }
];
