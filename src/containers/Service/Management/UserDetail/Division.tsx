import React, {Component, useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Dropdown, Form } from 'semantic-ui-react';

import * as cm_action from 'actions/commons';
import cm from 'helpers/commons';
import storage from 'helpers/storage';
import toastr from 'helpers/toastr';
import {Select, Space} from "antd";

//트리 변환 메서드
const treeModel = function (arrayList, rootId) {
  const rootNodes = [];
  const traverse = function (nodes, item, index) {
    if (nodes instanceof Array) {
      return nodes.some(function (node) {
        if (node.id === item.parentId) {
          node.children = node.children || [];
          return node.children.push(arrayList.splice(index, 1)[0]);
        }

        return traverse(node.children, item, index);
      });
    }
  };

  while (arrayList.length > 0) {
    arrayList.some(function (item, index) {
      if (item.parentId === rootId) {
        return rootNodes.push(arrayList.splice(index, 1)[0]);
      }

      return traverse(rootNodes, item, index);
    });
  }

  return rootNodes;
};

const Division = ({ cmAPI, defaultValue, onChangeFunc, onChange, value: handleValue,
                    isDim, isSearch,
                    staffDivisionList, divisionList, label, inputStyle, style, isLabel, placeholderText
                  }) => {

  const [valueState, setValueState] = useState({
    depth1: null
  });
  const [onchange, setOnchange] = useState(false);

  useEffect(() => {
    cmAPI.divisionChange({
      type: 'depth',
      name: 'value',
      value: null
    });
  }, []);

  useEffect(() => {
    if (staffDivisionList && defaultValue && defaultValue.length > 0 && !onchange) {
      const { division } = staffDivisionList.data;
      defaultSetDivision(division).then();
    } else if (staffDivisionList && onchange && !defaultValue) {
      setOnchange(false);
      setValueState({
        depth1: null
      });
    }
  }, [staffDivisionList, defaultValue]);

  const divisionClick = (val, idx) => {
    let value = val;

    if (value == 0 && idx != 1) {
      value = valueState['selValue' + (idx - 1)];
    }
    const orgCode = cm.divisionOrgIdx(value);
    try {
      onChange(value);
      cmAPI.divisionChange({
        type: 'depth',
        name: 'value',
        value: value === 0 ? null : value
      });

      cmAPI.divisionChange({
        type: 'orgIdx',
        name: 'value',
        value: orgCode
      });
    } catch (e) {
      console.error('###### e : ', e);
    } finally {
      if (onChangeFunc) onChangeFunc();
    }
  };

  const defaultSetDivision = async (divisions) => {
    let dataSet = [];
    let data = [];
    let newValue = {};

    setOnchange(true);

    defaultValue.forEach((orgCode, i) => {
      let index = 0;
      let division = divisions;
      let depth = [];

      if (defaultValue.length == i + 1) {

        cmAPI.divisionChange({
          type: 'depth',
          name: 'value',
          value: orgCode
        });
        onChange(orgCode);
      }

      if (dataSet.length > 0) {
        let temp = dataSet[i - 1];
        let cIdx = temp['depth' + i].cIdx;
        let tempdivision = temp['depth' + i].tempdivision[cIdx - 1].division;
        division = tempdivision;
      }

      data['value'] = orgCode;
      division.some((item, idx) => {
        if (item.orgCode === orgCode) {
          data['index'] = idx;

          depth.push({
            value: 0,
            text: '부서 전체'
          });

          //선택 된 부서 하위 부서가 있을 시
          if (item.division) {
            //하위 부서를 depth에 push
            item.division.map((data) => {
              depth.push({
                value: data.orgCode,
                text: data.name
              });
            });
          }
        }
      });


      newValue = {
        ...newValue,
        ['depth' + (i + 1)]: {
          depth: depth,
          cIdx: data.index + 1,
          tempdivision: division
        },
        ['selValue' + (i + 1)]: data.value
      }
      dataSet.push({
        ['depth' + (i + 1)]: {
          depth: depth,
          cIdx: data.index + 1,
          tempdivision: division
        },
        ['selValue' + (i + 1)]: data.value
      });
    });

    setValueState({
      ...valueState,
      ...newValue
    });
  };

  const divisionDepth = async (event, data, division, idx) => {
    let totalDepth = storage.get('depth');

    //현재 부서의 하위 부서를 모두 삭제
    const valueStateClone = { ...valueState };
    for (let i = idx; i <= totalDepth; i++) {
      if (valueStateClone['depth' + i]) {
        valueStateClone['depth' + i] = undefined;
        valueStateClone['selValue' + i] = undefined;
      }
    }
    setValueState(valueStateClone);

    let depth = [];

    //선택 된 부서 위치 index
    let c = null;

    if (!event) {
      c = data.index;
    } else {
      c =
        $(event.target).prop('tagName') !== 'SPAN'
          ? $(event.target).index()
          : $(event.target)
            .parent()
            .index();
      c = c === 0 ? 1 : c;
    }

    //현재 선택 된 부서 depth
    let tempdivision = division;

    //첫 depth가 아닐 때
    if (idx !== 1) {
      // 현재 부서 하위 depth
      tempdivision = division.division;
    }

    tempdivision.some((item, idx) => {
      if (item.orgCode === data.value) {
        //default
        depth.push({
          value: 0,
          text: '부서 전체'
        });

        //선택 된 부서 하위 부서가 있을 시
        if (item.division) {
          //하위 부서를 depth에 push
          item.division.map((data) => {
            depth.push({
              value: data.orgCode,
              text: data.name
            });
          });
        }

        return;
      }
    });

    setValueState({
      ...valueState,
      ['depth' + idx]: {
        depth: depth,
        cIdx: c,
        tempdivision: tempdivision
      },
      ['selValue' + idx]: data.value
    });
    divisionClick(data.value, idx);
  };

  const divisionFormRender = (division, inputStyle) => {

    let totalDepth = storage.get('depth');
    let form = [];
    const depth = [];
    const temp = [];

    for (let i = 0; i < totalDepth; i++) {
      const defaultOptions = { value: 0, text: placeholderText ? placeholderText : `부서 ${i + 1}${i === 0 ? ' 선택': ''}` };

      let isShow = false;
      let cIdx = 0;
      let tempdivision = null;
      let lowDepth = valueState['depth' + i];
      let selValue = valueState['selValue' + i] ? valueState['selValue' + i] : 0;

      if (i === 0) {
        depth.push(defaultOptions);

        //depth 1 데이터
        (division ?? []).map((data) => {
          depth.push({
            value: data.orgCode,
            text: data.name
          });
        });
        isShow = true;
      } else {
        // 상위 depth 데이터가 있을 시
        if (lowDepth) {
          // 선택한 부서의 index
          cIdx = valueState['depth' + i].cIdx - 1;
          // 현재 부서
          tempdivision = lowDepth.tempdivision;
          //하위 부서가 있을 시
          if (lowDepth.depth.length > 1) {
            isShow = true;
          } else {
            lowDepth = {
              depth: [defaultOptions]
            };
          }

          // 상위 depth가 선택 되지 않았을 때 default depth;
        } else {
          lowDepth = {
            depth: [defaultOptions]
          };
        }
      }
      form.push(
        <Dropdown
          className="division-selectbox"
          key={i}
          placeholder={placeholderText ? placeholderText : '부서 ' + (i + 1)}
          search={isSearch}
          style={inputStyle}
          options={lowDepth ? lowDepth.depth : depth}
          value={valueState['selValue' + (i + 1)] ? valueState['selValue' + (i + 1)] : 0}
          disabled={!isShow || isDim}
          onChange={
            lowDepth
              ? (event, data) => divisionDepth(event, data, tempdivision[cIdx], i + 1)
              : (event, data) => divisionDepth(event, data, division, i + 1)
          }
          selection
        />
      );
    }

    return form;
  };

  const depth1 = [],
    defaultOptions = [{ value: 0, text: placeholderText ? placeholderText : '부서 1' }];

  let def = 0;
  let divisionForm = null;
  let staffDivisionForm = null;

  if (staffDivisionList) {
    const { division } = staffDivisionList.data;
    staffDivisionForm = divisionFormRender(division, inputStyle);
  } else if (divisionList) {
    const { division } = divisionList.data;
    divisionForm = divisionFormRender(division, inputStyle);
  }

  return (
    <div>
      {staffDivisionList ? (
        <Form.Field inline style={style} className="com-selectbox">
          {isLabel ? <label style={label}>부서1 *</label> : null}
          {staffDivisionList ? (
            staffDivisionForm
          ) : (
            <Dropdown
              placeholder={placeholderText ? placeholderText : '부서 1 선택'}
              style={inputStyle}
              selection
              disabled
              defaultValue={0}
              key={0}
              options={defaultOptions}
            />
          )}
        </Form.Field>
      ) : (
        <Form.Field inline style={style} className="com-selectbox">
          {isLabel ? <label style={label}>부서1 *</label> : null}
          {divisionList ? (
            divisionForm
          ) : (
            <Dropdown
              placeholder={placeholderText ? placeholderText : '부서 1 선택'}
              style={inputStyle}
              selection
              disabled
              defaultValue={0}
              key={0}
              options={defaultOptions}
            />
          )}
        </Form.Field>
      )}
    </div>
  );
};


const mapStateToProps = (state) => ({
  depth: state.commons.depth,
  divisionList: state.commons.staffDivision
});

const mapDispatchToProps = (dispatch) => ({
  cmAPI: bindActionCreators(
    {
      divisionChange: cm_action.ReduxDataSet
    },
    dispatch
  )
});

export default connect(mapStateToProps, mapDispatchToProps)(Division);
