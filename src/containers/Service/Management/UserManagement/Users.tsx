import React, { ReactElement, useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import useFetch from 'use-http';

import api from 'config';
import { FormattedMessage } from 'react-intl';

import OptionBar from 'vcomponents/OptionBar';
import Table from 'vcomponents/Table';
import { PageContent } from 'styles/global';
import * as Title from 'vcomponents/Forms/Title';
import { LeftArea } from 'components/StaffManagement';

import * as companyAction from 'actions/company';
import * as commonAction from 'actions/commons';
import toastr from 'helpers/toastr';
import common from 'helpers/commons';
import messages from './messages';
import { columns, optionBarTemplate } from './viewMeta';
import { Container, ContentWrapper } from './styles';
import { defaultAction } from './actions';
import {Commons} from "../../../../components";

interface UsersProps {}

function Users({ auth, status, divisionUserCnt, staffDivisionList, cmAPI, cpAPI, location }: UsersProps): ReactElement {
  const [todos, setTodos] = useState(undefined);
  const [isLoadingBar, setIsLoadingBar] = useState(false);
  const [param, setParam] = useState({
    keyword: null,
    state: null,
    auth: null,
    orgCode: null,
    groupid: null,
    viewType: 'all',
    dormantTargetFilter: false
  });
  const [userTotal, setUserTotal] = useState(0);
  const [userCnt, setUserCnt] = useState(0);
  const { get, post, data, response, loading, error } = useFetch(`${api.config.companyAPI}/captain-payment/member/v1`);

  useEffect(() => {
    (async () => initialData())();
  }, []);

  useEffect(() => {
    (async () => staffSel())();
  }, [param]);

  async function initialData() {

    if (divisionUserCnt && userTotal === 0) {
      let cnt = 0;
      divisionUserCnt.forEach((item) => {
        cnt += item.userCnt;
      });
      if (cnt > userTotal) {
        setUserTotal(cnt);
      }
    }
    // this.positionSel(); // 직위 조회 TODO
  }

  // 사용자 조회
  const staffSel = async (params = param) => {
    isLoadingBarChange(true);
    try {
      await get(
        `/?${Object.entries({ ...params, type: 'all' })
          .map((e) => e.join('='))
          .join('&')}`
      );
      if (response.ok) {
        const staffList = await response.json();
        setTodos(staffList);
        if (staffList && staffList.data) {
          const { paging } = staffList.data;
          setUserCnt(paging.totalcount);
        }
      }
      isLoadingBarChange(false);
    } catch (e) {
      console.log('###### staffSel error : ', e);
    }
  };

  // 부서별 사용자 조회
  const divisionStaffSel = async (_orgCode) => {
    const orgCode = _orgCode;
    const params = {
      keyword: param.keyword,
      orgCode,
      viewType: param.viewType,
      status: status ? status.value : null,
      grade: auth ? auth.value : null,
      dormantTargetFilter: param.dormantTargetFilter
    };
    // await staffSel(params);
    // setParam({
    //   ...param,
    //   orgCode
    // });
    setParam({
      ...param,
      ...params
    });
    // this.setState({
    //   param: update(this.state.param, { orgCode: { $set: orgCode } }),
    //   staffCheckedData: []
    //   // , isChecked : false
    // });
    // TODO checkbox clear

    await isLoadingBarChange(false);
  };

  const staffDivisionSel = async () => {
    const params = { viewType: 'all' };

    try {
      await cmAPI.staffDivisionSel(params);

      if (staffDivisionList && staffDivisionList.data) {
        const { division, depth } = staffDivisionList.data;
        const navi = common.divisionNavigation(division);

        common.staffDivisionNavigation(depth, navi);
      }
    } catch (e) {
      const { response } = e;
      if (response) {
        toastr('top-right', 'error', response.data.message);
      }
      console.log('###### staffDivision call error : ', e);
    }
  };

  // 부서 수정
  const divisionMod = async (params) => {
    try {
      if (!params.id) {
        params.id = param.orgCode;
      }
      await cpAPI.divisionMod(params);
      staffDivisionSel();
      // return true;
    } catch (e) {
      const { response } = e;
      if (response) {
        toastr('top-right', 'error', response.data.message);
      }
    }
  };

  // 부서 삭제
  const divisionDel = async () => {
    try {
      const params = {
        id: param.orgCode
      };

      await cpAPI.divisionDel(params);
      await staffDivisionSel();
      // return true;
    } catch (e) {
      const { response } = e;

      if (response) {
        toastr('top-right', 'error', response.data.message);
      }

      console.log('###### divisionSortDel error : ', e);
    }
  };

  // 부서 추가
  const divisionAdd = async (params) => {
    try {
      await cpAPI.divisionAdd(params);
      await staffDivisionSel();
      // return true;
    } catch (e) {
      const { response } = e;

      if (response) {
        toastr('top-right', 'error', response.data.message);
      }

      console.log('###### divisionAdd error : ', e);
    }
  };

  // 오름차순
  const divisionAscending = async (params) => {
    try {
      if (!params.id) {
        params.id = param.orgCode;
      }

      await cpAPI.divisionMod(params);
      await staffDivisionSel();
      // return true;
    } catch (e) {
      const { response } = e;

      if (response) {
        toastr('top-right', 'error', response.data.message);
      }

      console.log('###### divisionAscending error : ', e);
    }
  };

  // 부서별 사용자 카운트
  const divisionUserCntSel = async (params) => {
    // this.isLoadingBarChange(true);
    try {
      await cpAPI.divisionUserCntSel(params);
      if (userTotal === 0) {
        let cnt = 0;
        divisionUserCnt.forEach((item) => {
          cnt += item.userCnt;
        });
        if (cnt > userTotal) {
          setUserTotal(cnt);
          optionBarTemplate[0].defaultValue = cnt;
        }
      }
    } catch (e) {
      const { response } = e;

      if (response) {
        toastr('top-right', 'error', response.data.message);
      }
      console.log('###### divisionUserCntSel error : ', e);
    } finally {
      // this.isLoadingBarChange(false);
    }
  };

  const isLoadingBarChange = (isShow) => {
    setIsLoadingBar(isShow);
  };

  return (
    <Container>
      <OptionBar template={optionBarTemplate} colInRow={5} />
      <Table columns={columns} />
    </Container>
  );
}

const mapStateToProps = (state) => ({
  staffDivisionList: state.commons.staffDivision,
  staffList: state.commons.staff,
  depth: state.commons.depth,
  groupIdx: state.commons.group,
  divisionUserCnt: state.company.divisionUserCnt, // 부서별 사용자 수(카운트)
  auth: state.commons.auth,
  text: state.commons.text,
  status: state.commons.status
});

const mapDispatchToProps = (dispatch, ownProps) => ({
  cpAPI: bindActionCreators(
    {
      positionSel: companyAction.PositionSel,
      positionAdd: companyAction.PositionAdd,
      positionMod: companyAction.PositionMod,
      positionDel: companyAction.PositionDel,
      staffBulkMod: companyAction.StaffBulkMod,
      divisionMod: companyAction.DivisionSortMod,
      divisionDel: companyAction.DivisionDel,
      divisionAdd: companyAction.DivisionAdd,
      divisionUserCntSel: companyAction.DivisionUserCnt,
      changeInfoSel: companyAction.ChangeInfoSel,
      cancelReserveDel: companyAction.CancelReserve
    },
    dispatch
  ),

  cmAPI: bindActionCreators(
    {
      sikdaeGroupSel: commonAction.SikdaeGroupList,
      staffSel: commonAction.StaffSel,
      staffDivisionSel: commonAction.StaffDivisionList
    },
    dispatch
  )
});

export default connect(mapStateToProps, mapDispatchToProps)(Users);
