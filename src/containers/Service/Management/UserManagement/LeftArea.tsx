import React, { ReactElement, useEffect, useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Icon, Menu, Segment, Button } from 'semantic-ui-react';
import update from 'react-addons-update';

import { SortableDivision } from 'components/StaffManagement';
import CommonModal from 'components/StaffManagement/Modal/CommonModal';
import styled from 'styled-components';

import toastr from 'helpers/toastr';
import cm from 'helpers/commons';

import GroupList from './GroupList';
import { LeftAreaContainer } from './styles';

interface LeftAreaProps {
  data;
  company;
  fn;
  staffDivisionList;
  requireMealGroup;
}

const sss = React.memo(
  () => {},
  (prevProps, nextProps) => {
    return nextProps.value === prevProps.value;
  }
);

function LeftArea(props: LeftAreaProps): ReactElement {
  const [orgCodes, setOrgCodes] = useState([]);
  const [open, setOpen] = useState(false);
  const [dimmer, setDimmer] = useState(undefined);
  const [openModalOrgCode, setOpenModalOrgCode] = useState(null);
  const [size, setSize] = useState('small');
  const [header, setHeader] = useState(null);

  useEffect(() => {
    document.querySelector('.ui.bottom.attached.segment').style.height = 'calc(100% - 100px)';
  }, []);

  /**
   * [인사 및 부서권한을 가진 사용자 부서에 대한 권한 체크]
   * @return {[boolean]} [권한 유무]
   */
  const authCheck = () => {
    const { data, company } = props;
    let result = false;

    if (company && company.data) {
      const { auth } = company.data;
      const { orgCode } = data.param; // 선택 된 부서
      const authType = auth.type; // 권한 타입=
      const authCheck = auth.auth.map((item) => item.code).indexOf('employee:create');

      if (authType === 'DIVISION') {
        // 인사 및 부서권한을 가진 사용자 체크
        if (!data.param.orgCode && authCheck === -1) {
          return true;
        }
        result = cm.authDivisionCheck(auth, orgCode);
      }
    }
    return result;
  };

  const ShowModalMod = (dimmer, header) => {
    const { data, fn } = props;

    const result = authCheck();
    if (header === '부서명 변경' && !data.param.orgCode) {
      toastr('top-right', 'warning', '부서를 선택하세요.');
      return;
    }

    // 권한 체크 유무
    if (result) {
      toastr('top-right', 'warning', '해당 부서권한이 없습니다.');
      return;
    }

    console.log('modal open!', data.param.orgCode);
    setOpen(true);
    setDimmer(dimmer);
    setOpenModalOrgCode(data.param.orgCode);
    setHeader(header);
    setSize('small');
    // this.setState({
    //   data
    // });
  };

  const divisionFunc = (params, type) => {
    const { fn, data } = props;
    let result = false;

    if (type === 'mod') {
      result = fn.divisionMod(params);
    } else if (type === 'del') {
      result = fn.divisionDel();
    } else if (type === 'add') {
      setOpen(false);
      result = fn.divisionAdd(params);
    }
  };

  const closeModal = () => {
    setOpen(false);
    setDimmer(undefined);
    setOpenModalOrgCode(null);
  };

  const orgCodePut = async (orgCode, depth) => {
    const orgCodesSize = orgCodes.length;
    const newOrgCodes = update(orgCodes, { $splice: [[depth - 1, orgCodesSize]] });
    if (depth === 0) {
      await setOrgCodes([]);
    } else if (depth === 1) {
      await setOrgCodes([orgCode]);
    } else {
      await setOrgCodes(update(newOrgCodes, { $push: [orgCode] }));
    }
  };

  const tabMove = (type) => {
    const { fn } = props;
    if (type === 'group') {
      setOrgCodes([]);
    }
    fn.tabMove(type);
  };

  const scrollMove = (e) => {
    $('.actions-btn').css('position', 'sticky');
  };

  const { fn, data, staffDivisionList, company, requireMealGroup = true } = props;

  const modal = {
    open: open,
    size: size,
    dimmer: dimmer,
    header: header,
    close: closeModal,
    openModalOrgCode: openModalOrgCode
  };
  const items = staffDivisionList && staffDivisionList.data ? staffDivisionList.data.division : null;
  return (
    <LeftAreaContainer>
      <div style={{ height: '700px' }}>
        <Menu fluid widths={2} tabular className="vendysFullTab" attached="top">
          <Menu.Item active={data.tabType === 'division'} onClick={(e) => tabMove('division')}>
            <span>부서</span>
          </Menu.Item>
          {requireMealGroup ? (
            <Menu.Item active={data.tabType === 'group'} onClick={(e) => tabMove('group')}>
              <span>복지그룹</span>
            </Menu.Item>
          ) : null}
        </Menu>
        <Segment
          attached="bottom"
          onScroll={scrollMove}
          style={{ overflowY: 'scroll', overflowX: 'hidden', marginBottom: 0, height: 'calc(100% - 60px)' }}
        >
          {data.tabType === 'division' ? (
            items ? (
              <SortableDivision
                items={items}
                divisionStaffSel={fn.divisionStaffSel}
                divisionMod={fn.divisionMod}
                divisionAdd={fn.divisionAdd}
                divisionDel={fn.divisionDel}
                divisionAscending={fn.divisionAscending}
                isLoadingBarChange={fn.isLoadingBarChange}
                divisionUserCntSel={fn.divisionUserCntSel}
                modal={modal}
                userTotal={data.userTotal}
                orgCodePut={orgCodePut}
                orgCodeChange={fn.orgCodeChange}
                orgCodes={orgCodes}
                isLoadingBar={data.isLoadingBar}
              />
            ) : (
              <ContentWrapper2 depth={true}>
                <ItemDiv2
                  className="line"
                  // onClick={fn.divisionStaffSel}
                  selectedOrgCode={null}
                  isAllClick
                  // itemWidth={itemWidth}
                >
                  <Icon disabled name="minus" style={{ fontSize: '24px', margin: '0 0 -5px 5px' }} />
                  <span>{`전체(${0})`}</span>
                </ItemDiv2>
                {modal.open ? <CommonModal orgCode={null} fn={null} modal={modal} divisionFunc={divisionFunc} /> : null}
              </ContentWrapper2>
            )
          ) : (
            <GroupList onClick={fn.groupStaffList} groupData={data.welfareGroup} totalCount={data.userTotal} />
          )}
        </Segment>
        <div>
          <div className="actions-btn">
            <Button.Group fluid>
              <Button
                inverted
                disabled={data.tabType !== 'division'}
                color="grey"
                onClick={(e) => ShowModalMod(true, '부서명 변경')}
              >
                수정
              </Button>
              <Button
                inverted
                disabled={data.tabType !== 'division'}
                color="grey"
                onClick={(e) => ShowModalMod(true, '부서 삭제')}
              >
                삭제
              </Button>
              <Button
                inverted
                disabled={data.tabType !== 'division'}
                color="grey"
                onClick={(e) => ShowModalMod(true, '부서 추가')}
              >
                추가
              </Button>
            </Button.Group>
            <Button /* 한국타이어만 노출하도록 처리 (추후에 하드코딩 영역 수정 필요) */
              inverted
              disabled={data.tabType !== 'division'}
              color="grey"
              fluid
              onClick={(e) => ShowModalMod(true, '오름차순')}
            >
              부서 정렬 초기화
            </Button>
          </div>
        </div>
      </div>
    </LeftAreaContainer>
  );
}

function moviePropsAreEqual(prev, next) {
  // const divisionChange = prev.staffDivisionList !== next.staffDivisionList;
  // const openChange = prev.open !== next.open;
  // const orgCodesChange = prev.orgCodes !== next.orgCodes;
  // return !divisionChange || !prev.staffDivisionList || openChange || !orgCodesChange;
}

const mapStateToProps = (state) => ({
  staffDivisionList: state.commons.staffDivision,
  company: state.auth.company
});

const mapDispatchToProps = (dispatch, ownProps) => ({});

export default React.memo(connect(mapStateToProps, mapDispatchToProps)(LeftArea), moviePropsAreEqual);

const ContentWrapper2 = styled.div`
  display: table;
  width: 100%;
  & > div > em {
    display: block;
  }
  border-bottom: ${(props) => (props.depth ? '1px solid #efefef' : null)};
`;

const ItemDiv2 = styled.div`
  height: 40px;
  display: flex;
  padding-left: 5px;
  border-top: 1px solid #efefef;
  border-bottom: 1px solid #efefef;
  align-items: center;
  border-bottom: 1px solid #efefef;
  z-index: 999;
  cursor: pointer;
  width: ${(props) => `${props.itemWidth}px`};
  background: ${(props) => (props.isAllClick && !props.selectedOrgCode ? '#0bb656' : '#ffffff')};
  color: ${(props) => (props.isAllClick && !props.selectedOrgCode ? '#ffffff' : null)};
`;
