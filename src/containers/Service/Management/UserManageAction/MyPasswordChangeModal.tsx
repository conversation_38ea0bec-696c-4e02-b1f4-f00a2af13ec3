import React, { useState } from 'react';
import ModalComponent from 'vcomponents/ModalComponent';
import { FormattedMessageDynamic } from 'utils';
import { FormattedMessage } from 'react-intl';
import { withModalContext } from 'context/ModalContext';
import { useMutation } from 'react-query';
import {Button, message as msgAlert, Space} from 'antd';
import FormBuilder from 'vcomponents/FormBuilder/index2';
import base64 from 'helpers/base64';
import { myPasswordChangeColumns } from './viewMeta';
import { changeMyPassword } from './state';
import messages from './messages';

const MyPasswordChangeModal = ({ context }) => {
  const onSuccess = () => {
    msgAlert.success('비밀번호가 재설정 되었습니다.');
    context.hideModal('myPasswordChange');
  };

  const { mutate } = useMutation('myPasswordChange', (data) => changeMyPassword(data), { onSuccess });
  const [value, setValue] = useState({});
  const onChange = (k, v, a) => {
    setValue({
      ...a
    });
  };

  const save = () => {
    const { source, target, targetConfirm } = value;
    if (!source) {
      msgAlert.warning('현재 비밀번호를 입력하세요.');
      return;
    }
    if (!target) {
      msgAlert.warning('변경할 비밀번호를 입력하세요.');
      return;
    }
    if (target !== targetConfirm) {
      msgAlert.warn('재입력한 변경할 비밀번호가 일치하지 않습니다.');
      return;
    }
    const data = {
      source: base64.encode(source),
      target: base64.encode(target)
    };
    mutate(data);
  };
  return (
    <FormattedMessage {...messages.changePassword}>
      {(msg) => {
        return (
          <>
            <Button onClick={() => context.showModal('myPasswordChange')}>{msg}</Button>
            <ModalComponent
              title={msg}
              component={<Popup onChange={onChange} />}
              id="myPasswordChange"
              width="50%"
              handleOk={save}
              handleCancel={() => context.hideModal('myPasswordChange')}
              okText={<FormattedMessageDynamic id="complete" />}
              cancelText={<FormattedMessageDynamic id="cancel" />}
            />
          </>
        );
      }}
    </FormattedMessage>
  );
};

const Popup = ({ onChange }) => {
  return (
    <Space direction="vertical">
      <FormBuilder name="myPasswordChange" columns={myPasswordChangeColumns} onValuesChange={onChange} />
    </Space>
  );
};

export default withModalContext(MyPasswordChangeModal);
