import React, {useCallback, useMemo, useState} from 'react';
import ModalComponent from 'vcomponents/ModalComponent';
import {FormattedMessageDynamic, transMeta} from 'utils';
import { FormattedMessage } from 'react-intl';
import { withModalContext } from 'context/ModalContext';
import messages from './messages';
import { useMutation } from 'react-query';
import { resetPassword } from './state';
import useAction from './hooks';
import {Form, Space, Typography, message as msgAlert } from "antd";
import FormBuilder from "vcomponents/FormBuilder/index2";
import {passwordResetColumns} from "./viewMeta";
import FormPanel from "vcomponents/Form/FormPanel";
import base64 from 'helpers/base64';

const PasswordResetModal = ({ userId, context }) => {
  const onSuccess = () => {
    msgAlert.success('비밀번호가 초기화 되었습니다.');
    context.hideModal('passwordReset');
  };

  const { mutate } = useMutation('passwordReset', (data) => resetPassword(userId, data), { onSuccess });
  const [value, setValue] = useState({});
  const onChange = (k, v, a) => {
    setValue({
      ...a
    });
  };

  const save = () => {
    const { password, passwordConfirm } = value;
    if (!password) {
      msgAlert.warning('초기화 비밀번호를 입력하세요.');
      return;
    }
    if (password !== passwordConfirm) {
      msgAlert.warn('재입력한 비밀번호가 일치하지 않습니다.');
      return;
    }
    const data = {
      id: userId,
      password: base64.encode(password)
    };
    mutate(data);
  };
  return (
    <FormattedMessage {...messages.passwordRest}>
      {(msg) => {
        return (
          <ModalComponent
            title={msg}
            component={<Popup onChange={onChange} />}
            id="passwordRest"
            width="50%"
            handleOk={save}
            handleCancel={() => context.hideModal('passwordRest')}
            okText={<FormattedMessageDynamic id="complete" />}
            cancelText={<FormattedMessageDynamic id="cancel" />}
          />
        );
      }}
    </FormattedMessage>
  );
};

const Popup = ({ onChange }) => {
  return (
    <Space direction="vertical">
      <FormBuilder name="passwordReset" columns={passwordResetColumns} onValuesChange={onChange} />
      <Typography.Text>비밀번호 초기화 후, 사용자가 최초 로그인시, 반드시 비밀번호 재설정 과정을 거치게 됩니다.</Typography.Text>
    </Space>
  );
}

export default withModalContext(PasswordResetModal);
