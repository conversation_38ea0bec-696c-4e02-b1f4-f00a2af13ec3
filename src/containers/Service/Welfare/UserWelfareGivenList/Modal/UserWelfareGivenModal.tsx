import React, { ReactElement, useEffect, useState } from 'react';
import { Modal, Row, Col, Button, InputNumber } from 'antd';
import moment from 'moment';

import { transMeta } from 'utils';
import FormBuilder from 'vcomponents/FormBuilder/index2';
import ViewPanel from 'vcomponents/Form/ViewPanel';
import Table from 'vcomponents/Table';
import { comma } from 'utils/comma';

import { welDtlModalColumns1, welDtlModalColumns2, welDetailDataColumns, defaultData } from '../viewMeta';

interface UserWelfareGivenModalProps {
  modalFn;
}

function UserWelfareGivenModal(props: UserWelfareGivenModalProps): ReactElement {
  const { modalFn } = props;
  const { modal, closeModal } = modalFn;
  const [detailData, setDetailData] = useState({});
  const [data, setData] = useState([]);

  useEffect(() => {
    const { dltData, rowData } = modalFn;

    if (dltData && Object.keys(dltData).length) {
      const obj = {
        payId: dltData.room.id,
        couponid: dltData.room.couponid,
        name: rowData.user.name,
        usedate: moment(dltData.room.usedate).format('YYYY-MM-DD HH:mm'),
        storeName: dltData.store.name,
        ...dltData.room.pay,
        ...dltData.room.price,
        ...dltData.room.addons
      };

      const result = [];
      dltData.user.forEach((row) => {
        const { policy } = row.group;
        result.push({ ...row, amount: policy[0].amount, policy: policy[0].name, id: policy[0].id });
        for (let i = 1; i < policy.length; i++) {
          result.push({ ...defaultData, amount: policy[i].amount, policy: policy[i].name, id: policy[i].id });
        }
      });
      result.push({ ...defaultData, signid: '총결제액', amount: obj.pay });

      setData(result);
      setDetailData({ ...dltData, data: obj });
    }
  }, [modalFn]);

  return (
    <div>
      <Modal
        key="welfareGivenModal"
        title="복지포인트 사용 내역 상세"
        visible={modal}
        onOk={closeModal}
        onCancel={closeModal}
        closable={false}
        width={1000}
        footer={[
          <Button type="primary" key="close" onClick={closeModal}>
            닫기
          </Button>
        ]}
      >
        <Row>
          <Col span={12}>
            <FormBuilder columns={transMeta(welDtlModalColumns1)} data={detailData.data} theme="none">
              <InputNumber
                id="com"
                formatter={(value) => comma(value)}
                bordered={false}
                style={{ background: '#fff' }}
              />
            </FormBuilder>
          </Col>
          <Col span={12}>
            <FormBuilder columns={transMeta(welDtlModalColumns2)} data={detailData.data} theme="none">
              <InputNumber
                id="com"
                formatter={(value) => comma(value)}
                bordered={false}
                style={{ background: '#fff' }}
              />
            </FormBuilder>
          </Col>
        </Row>

        <ViewPanel title="결제 상세정보" theme="none">
          <Table
            columns={welDetailDataColumns}
            data={data || []}
            style={{ width: '100%', padding: 0 }}
            onChange={(d) => {}}
            pageInfo={{}}
          />
        </ViewPanel>
      </Modal>
    </div>
  );
}

export default UserWelfareGivenModal;
