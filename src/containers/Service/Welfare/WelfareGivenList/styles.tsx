import styled from 'styled-components';
import Spin from 'vcomponents/Spin';
import { Modal, Input } from 'antd';

const { Search } = Input;

export const Gap = styled.div`
  .ant-space.ant-space-vertical {
    gap: 0px !important;
  }

  & .btnGroup {
    float: right;
    padding-right: 12px;
  }
`;

export const Container = styled(Spin)`
  //
`;

export const CustomModal = styled(Modal)`
  & .ant-modal-body {
    padding: 0px;
  }

  & .dltBtn {
    padding: 0px 5.88px;

    button {
      margin-left: 40px;
    }
  }

  & .amount {
    padding: 0px 5.88px;
  }

  & .adminUserName {
    padding: 0px 5.88px;
  }
`;

export const SearchInput = styled(Search)`
  width: 200px;
  margin-bottom: 15px;

  input {
    height: 32px;
    border-radius: 0 !important;
  }
`;

export const StyledBox = styled.div`
  & .status {
    padding: 12px 16px;
  }

  .ant-form-item-label {
    font-weight: bold;
    background: #f5f5f5;
    padding: 4px 10px;
    min-width: 170px;
  }

  .ant-form-item {
    margin-bottom: 0;
  }
`;

export const ModalSpin = styled(Spin)``;

export const StylesTable = styled.div`
  .table {
    .tr:last-child {
      background: #ededed;
      font-weight: 700;
    }
  }
`;
