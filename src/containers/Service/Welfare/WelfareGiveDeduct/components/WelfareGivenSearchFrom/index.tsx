/**
 *
 * WelfareGivenSearchFrom
 *
 */
import React, { useState } from 'react';

import Division from 'components/Division';
import OptionBar from 'vcomponents/OptionBar';
import { SearchFormContainer, SearchOptionsBarContainer } from './styles';

interface WelfareGivenSearchFromProps {
  search;
  columns;
  checkedCount;
  userTotal;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
function WelfareGivenSearchFrom(props: WelfareGivenSearchFromProps) {
  const { search, checkedCount, userTotal, columns } = props;
  const [params, setParams] = useState({
    keyword: null,
    groupid: null,
    auth: null,
    orgCode: null
  });

  const onSearch = (optionParams) => {
    search({
      ...params,
      ...optionParams
    });
  };

  return (
    <SearchFormContainer>
      <SearchOptionsBarContainer>
        <span className="select-item">{`${checkedCount}명 / ${userTotal}명`}</span>
        <OptionBar template={columns} colInRow={4} onClick={onSearch} />
      </SearchOptionsBarContainer>
      <Division
        onChange={(v) => {
          setParams({
            ...params,
            orgCode: v
          });
        }}
      />
    </SearchFormContainer>
  );
}

export default WelfareGivenSearchFrom;
