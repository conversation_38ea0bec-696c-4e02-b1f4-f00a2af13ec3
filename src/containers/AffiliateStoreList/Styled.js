import styled from 'styled-components';

// eslint-disable-next-line import/prefer-default-export
export const FlexRowContainer = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  max-height: 750px;
`;

export const MapToggleContainer = styled.div`
  padding: 5px 30px 0 0;

  span:first-child {
    padding: 0 10px 3px 30px;
  }
  span:last-child {
    vertical-align: middle;
  }
`;

export const StoreListContainer = styled.div`
  flex: 1 1 auto;
  max-height: 750px;
  overflow: auto;
`;
