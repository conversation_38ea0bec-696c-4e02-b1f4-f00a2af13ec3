import React, { ReactElement, forwardRef, useMemo, useRef } from 'react';
import ReactGA from 'react-ga4';
import { useQuery } from 'react-query';
import moment from 'moment';
import { Col, Row, ButtonProps } from 'antd';

import { withModalContext } from 'context/ModalContext';
import { getBeltBanner, getRollingBanner } from 'apis/captainpayment/benefit';
import storage from 'helpers/storage';
import { gaId } from 'helpers/loggers';
import SurveyModal from 'components/Header/SurveyModal';

import CardView from './CardView';
import BeltBanner from './SubBanner';
import Footer from './Footer';
import ApplyCompleteModal from './ApplyCompleteModal';

import { ReactComponent as ArrowLeftIcon } from './img/icon/arrow_left.svg';
import { ReactComponent as ArrowRightIcon } from './img/icon/arrow_right.svg';

import { IBeltBanner, serviceMap, IRollingBanner } from './viewMeta';
import { Banner, ServiceWrapper, CarouselButton, MainBanner, Container } from './styles';

interface ServiceLandingProps {
  history: any;
}

interface CarouselButtonProps extends ButtonProps {
  currentSlide?: number;
  slideCount?: number;
}

const PrevArrowButton = forwardRef<HTMLButtonElement, CarouselButtonProps>(
  ({ currentSlide, slideCount, style, ...props }, ref) => {
    return <CarouselButton shape="circle" ref={ref} icon={<ArrowRightIcon />} style={style} {...props} />;
  }
);
const NextArrowButton = forwardRef<HTMLButtonElement, CarouselButtonProps>(
  ({ currentSlide, slideCount, style, ...props }, ref) => {
    return <CarouselButton shape="circle" ref={ref} icon={<ArrowLeftIcon />} style={style} {...props} />;
  }
);

function ServiceLanding({ history }: ServiceLandingProps): ReactElement {
  const carouselRef = useRef();
  const { data: beltBanner } = useQuery<IBeltBanner[]>('getBeltBanner', () => getBeltBanner());
  const { data: rollingBanner } = useQuery<IRollingBanner[]>('getRollingBanner', () => getRollingBanner());

  const serviceList = useMemo(() => {
    const companyInfo = storage.haveToGet('companyInfo');
    return companyInfo?.serviceList;
  }, []);

  const handleBannerClick = (banner: IRollingBanner) => {
    if (!banner.linkUrl) return;
    ReactGA.gtag('event', `banner_corp_click_${banner?.idx}`, {
      send_to: gaId.DMP_TRACKING_ID,
      category: banner.representativePhrase
    });

    if (banner.linkUrl.indexOf('http') >= 0) {
      window.open(banner.linkUrl);
    } else {
      history.push(banner.linkUrl);
    }
  };

  const mainBannerMap = useMemo(() => {
    return rollingBanner?.filter((banner) => moment().isBetween(banner.exposureStartDate, banner.exposureEndDate));
  }, [rollingBanner]);

  const handleAfterChange = (bannerIdx: number) => {
    const company = storage.haveToGet('company');
    const userId = company?.user.id;
    const activeBanner = mainBannerMap[bannerIdx];
    if (activeBanner) {
      ReactGA.gtag('event', `banner_corp_rolling_${activeBanner?.idx}`, {
        send_to: gaId.MARKETING_TRACKING_ID,
        user_id: userId,
        category: activeBanner?.representativePhrase
      });
    }
  };

  return (
    <Container>
      <MainBanner
        arrows
        autoplay
        autoplaySpeed={3000}
        prevArrow={<PrevArrowButton currentSlide={0} slideCount={mainBannerMap?.length} />}
        nextArrow={<NextArrowButton currentSlide={0} slideCount={mainBannerMap?.length} />}
        afterChange={handleAfterChange}
        ref={carouselRef}
      >
        {mainBannerMap?.map((banner) => (
          <Banner key={banner} onClick={() => handleBannerClick(banner)}>
            <img src={banner.bannerImgUrl} alt="" />
          </Banner>
        ))}
      </MainBanner>
      <Row justify="center">
        {beltBanner?.map((belt) => (
          <BeltBanner history={history} item={belt} key={belt.idx} />
        ))}
      </Row>
      <ServiceWrapper>
        <Row gutter={[{ xs: 36, sm: 24, md: 7, lg: 7 }, 24]}>
          {serviceMap.map((service) => {
            const serviceStatus = serviceList?.filter(({ serviceType }) => serviceType === service.serviceType)[0];
            return (
              <Col key={service.serviceType} span={8}>
                <CardView
                  url={service.url}
                  landingUrl={service.landingUrl}
                  image={service.image}
                  logoImage={service.logoImage}
                  desc={service.desc}
                  serviceType={service.serviceType}
                  status={serviceStatus?.status}
                  open={service?.open}
                  history={history}
                />
              </Col>
            );
          })}
        </Row>
      </ServiceWrapper>
      <Footer />
      <SurveyModal />
      <ApplyCompleteModal history={history} />
    </Container>
  );
}

export default withModalContext(ServiceLanding);
