export const utilSum = ()=>{

}

export const columns = [
    {
      Header: '회사 아이디',
      accessor: 'comid',
    },
]

export const ServiceList = [
  {
    img: 'https://cdn.senmoney.co.kr/news/photo/202105/10120_10100_647.png',
    title: '내가 찾던 비즈니스 퀵',
    info: '배송과정 확인부터 이용내역 조회, 간편 정산까지 ',
    tag: 'Hot'
  },
  {
    img: 'https://cdn.senmoney.co.kr/news/photo/202105/10120_10100_647.png',
    title: '기업용 플라워 주문시스템',
    info: '화환부터 꽃바구니까지 임직원의 축하와 위로를 함께',
    tag: 'Hot'
  },
  {
    img: 'https://cdn.senmoney.co.kr/news/photo/202105/10120_10100_647.png',
    title: '내가 찾던 비즈니스 퀵',
    info: '배송과정 확인부터 이용내역 조회, 간편 정산까지 ',
    tag: '서비스 준비중'
  },
  {
    img: 'https://cdn.senmoney.co.kr/news/photo/202105/10120_10100_647.png',
    title: 'Corporate Business',
    info: 'Corporate Business is build for new or any startup business company. It has available all section for corporate business agency company and others business purpose.',
    tag: 'Hot'
  },
  {
    img: 'https://cdn.senmoney.co.kr/news/photo/202105/10120_10100_647.png',
    title: 'Corporate Business',
    info: 'Corporate Business is build for new or any startup business company. It has available all section for corporate business agency company and others business purpose.',
    tag: 'Hot'
  },
  {
    img: 'https://cdn.senmoney.co.kr/news/photo/202105/10120_10100_647.png',
    title: 'Corporate Business',
    info: 'Corporate Business is build for new or any startup business company. It has available all section for corporate business agency company and others business purpose.',
    tag: 'Hot'
  },
]
