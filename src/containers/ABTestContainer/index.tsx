import React, { ReactElement, useState, useEffect } from 'react'
import useFetch from 'use-http'

import api from 'config'
import { FormattedMessage } from 'react-intl';

import messages from './messages';
import { Container } from './styles'

interface TestContainerProps {
    
}

export default function TestContainer({}: TestContainerProps): ReactElement {
    const [todos, setTodos] = useState(undefined)
    const { get, post, response, loading, error } = useFetch(`${api.config.companyAPI}`)
  
    useEffect(() => {
        initialData()
    }, [])

    async function initialData() {
        const initialTodos = await get('/stat/v1/company/division/top')
        if (response.ok) {
            var data = await response.json()
            setTodos(data)
        }
    }
    
    return (
        <Container>
          <div><FormattedMessage {...messages.header} /></div>
            {todos && JSON.stringify(todos)}
        </Container>
    )
}
