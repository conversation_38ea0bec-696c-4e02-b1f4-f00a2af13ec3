import React from 'react';
import ModalComponent from 'vcomponents/ModalComponent';
import { FormattedMessageDynamic } from 'utils';
import { FormattedMessage } from 'react-intl';
import { withModalContext } from 'context/ModalContext';
import { Button, Space, Typography } from 'antd';

const completeKey = 'service.use.apply.complete';

const ServiceApplyCompleteModal = ({ context }) => (
  <FormattedMessage id={completeKey}>
    {(msg) => {
      return (
        <>
          <ModalComponent
            title={msg}
            component={<PopupComplete />}
            id={completeKey}
            width="30%"
            footer={[
              <Button key="cancel" onClick={() => context.hideModal(completeKey)}>
                <FormattedMessageDynamic id="close" />
              </Button>
            ]}
          />
        </>
      );
    }}
  </FormattedMessage>
);

const PopupComplete = () => {
  return (
    <Space direction="vertical" size={12}>
      <Typography.Text>서비스 사용 신청이 완료되었습니다.</Typography.Text>
      <Typography.Text>식권대장 담당자가 24시간내에 연락드릴 예정입니다.</Typography.Text>
      <Typography.Text>좀더 신속한 진행을 희망하실 경우, 아래 고객센터로 문의 바랍니다.</Typography.Text>
      <Typography.Text strong>고객센터 1644-5047</Typography.Text>
    </Space>
  );
};

export default withModalContext(ServiceApplyCompleteModal);
