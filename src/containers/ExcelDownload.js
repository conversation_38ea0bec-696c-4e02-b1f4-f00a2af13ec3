import React, { Component, useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import download from 'downloadjs';
import { Button, Modal } from 'semantic-ui-react';
import { ExcelList } from 'components/ExcelDownloadList';

import * as cm_action from 'actions/commons';
import * as etc_action from 'actions/etc';

import storage from 'helpers/storage';
import toastr from 'helpers/toastr';
import cm from 'helpers/commons';
import api from 'config';

const pollingExcelDownload = (Child) => {
  return (props) => {
    const [parmas, setParmas] = useState({
      page: 1,
      pagerow: 15
    });
    const result = etc_action.usePollingExcelList(parmas);

    const onPageClick = (page) => {
      setParmas({ ...parmas, page });
    };

    return (
      <div>
        <Child {...props} onPageClick={onPageClick} list={result || {}} />
      </div>
    );
  };
};

class ExcelDownload extends Component {
  constructor(props) {
    super(props);

    this.state = {
      page: 1,
      pagerow: 15,
      isLoading: false,
      loading: false,
      open: false
    };
  }

  componentWillMount() {
    // this.excelList();
  }

  componentDidMount() {
    cm.mainTitle('엑셀 다운로드 내역');
  }

  componentDidUpdate(prevProps) {
    const { list } = this.props;
    const { list: prevList } = prevProps || {};
    if (prevList && prevList.data && list && list.data) {
      const { excel: prevExcel = [] } = prevList.data;
      const { excel = [] } = list.data;

      excel.forEach(({ idx, excelStatus }) => {
        const target = prevExcel.filter((item) => item.idx === idx && item.excelStatus !== excelStatus)[0];
        if (target && excelStatus === 'DOWNLOAD') {
          this.setState({ open: true });
        }
      });
    }
  }

  excelList = async () => {
    const { etcAPI } = this.props;
    const { page, pagerow } = this.state;
    await etcAPI.excelList({ page, pagerow });
  };

  pageClick = async (page) => {
    this.setState({ page: page });
    await this.props.onPageClick(page);
  };

  excelDownload = async (data) => {
    const com = storage.get('company');
    const { excelResult, cmAPI } = this.props;

    let header = null;
    try {
      await this.setState({
        downloadExcelFile: '',
        isLoading: true
      });

      const params = {
        com: com,
        excel: {
          url: '/addons/v1/excel/' + data.id,
          method: 'get'
        },
        excelResult: {
          excel: {
            name: data.name
          }
        }
      };

      await cmAPI.excelDownload(params);
      const { excelDownloadReault } = this.props;

      if (excelDownloadReault) {
        header = excelDownloadReault.headers['content-disposition'];
        header = header.split('=');

        download(excelDownloadReault.data, decodeURIComponent(header[1]));
        toastr('top-right', 'success', ' 엑셀파일 생성 완료 되었습니다.');
      }
    } catch (e) {
      const result = e.response;

      if (result && result.data) {
        let status = result.status;

        toastr('top-right', 'error', ' 엑셀파일 생성에 실패 하였습니다.(' + String(status) + ')');
      }
      console.log('###### ExcelDownLoad : ', e);
    } finally {
      this.onRefresh();
      await this.setState({
        isLoading: false
      });
    }
    // location.href = api.config.companyAPI + "/addons/v1/excel/" + data.id + "?name=" + data.name;
  };

  onRefresh = async () => {
    this.setState({ loading: true });
    await this.excelList();
    this.setState({ loading: false });
  };

  render() {
    const { page, pagerow, isLoading, loading, open } = this.state;
    const { list } = this.props;
    const excelFn = {
      page,
      pagerow,
      isLoading,
      list,
      loading,
      excelList: this.excelList,
      pageClick: this.pageClick,
      excelDownload: this.excelDownload
    };

    return (
      <main className="excel-download-pag">
        <div className="top-btn button">
          <Button.Group>
            <Button inverted color="black" onClick={this.onRefresh}>
              새로고침
            </Button>
          </Button.Group>
        </div>

        <ExcelList excelFn={excelFn} />

        <Modal onClose={() => this.setState({ open: false })} open={open} size="mini">
          <Modal.Header>파일 생성 완료</Modal.Header>
          <Modal.Content>
            <div>파일 생성이 완료되었습니다.</div>
            <div>다운로드 해주세요.</div>
          </Modal.Content>
          <Modal.Actions>
            <Button color="black" onClick={() => this.setState({ open: false })}>
              확인
            </Button>
          </Modal.Actions>
        </Modal>
      </main>
    );
  }
}

ExcelDownload = connect(
  (state) => ({
    // list: state.etc.list,
    download: state.etc.download,
    excelDownloadReault: state.commons.excelDownload
  }),
  (dispatch) => ({
    etcAPI: bindActionCreators(
      {
        excelList: etc_action.ExcelList,
        pollingExcelList: etc_action.PollingExcelList
        //excelDownload : etc_action.ExcelDownload
      },
      dispatch
    ),

    cmAPI: bindActionCreators(
      {
        excelDownload: cm_action.ExcelDownload
      },
      dispatch
    )
  })
)(ExcelDownload);

export default pollingExcelDownload(ExcelDownload);
