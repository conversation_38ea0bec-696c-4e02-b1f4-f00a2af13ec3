import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import moment from 'moment-timezone';
import { MyPointForm, MyPointResult } from 'components/MyPointList';

import * as cm_action from 'actions/commons';
import * as usage_action from 'actions/usage';
import cm from 'helpers/commons';
import toastr from 'helpers/toastr';
import storage from 'helpers/storage';

moment.tz.setDefault('Asia/Seoul');

class MyPointList extends Component {
  constructor() {
    super();

    this.state = {
      page: 1,
      pageRow: 15
    };
  }

  componentWillMount() {
    const comInfo = storage.get('company').info;

    // 대장 포인트를 사용하지 않는 회사면 dashBoard 화면으로 이동
    if (!comInfo.useMypoint) {
      this.context.router.history.push('/main/dashBoard');
    }
    const { usageAPI } = this.props;
    // 사용타입 코드
    usageAPI.pointType();
    // 상태 코드
    usageAPI.pointStatus();
  }

  componentDidMount() {
    cm.mainTitle('대장포인트 내역');
    this.mypointList();
  }

  // 확인
  submitForm = (e) => {
    e.preventDefault();

    const { dateCheck } = this.props;

    if (!dateCheck.isValue.isState) {
      toastr('top-right', 'warning', dateCheck.isValue.message);
      return;
    }

    this.mypointList();
  };

  handleEntSubmit = (e) => {
    if (e.key === 'Enter') {
      this.submitForm(e);
    }
  };

  // 내역 불러오기
  mypointList = () => {
    const { page, pageRow } = this.state;
    const { date, statusStr, depth, text } = this.props;

    const params = {
      startdate: date
        ? date.sDate
        : moment()
            .startOf('month')
            .format('LL'),
      enddate: date ? date.eDate : moment().format('LL'),
      type: statusStr ? statusStr.type : null,
      status: statusStr ? statusStr.status : null,
      orgCode: depth ? depth.value : null,
      keyword: text ? text.value : null,
      page,
      pagerow: pageRow
    };

    const { usageAPI } = this.props;
    usageAPI.pointList(params);

    const { cmAPI } = this.props;
    cmAPI.changeExcel({
      excel: params
    });
  };

  pageClick = async (page) => {
    await this.setState({ page });
    this.mypointList();
  };

  render() {
    const { page, pageRow } = this.state;
    const { division, list } = this.props;
    const formFn = {
      division,
      submitForm: this.submitForm,
      handleEntSubmit: this.handleEntSubmit
    };
    const resultFn = {
      list,
      page,
      pageRow,
      pageClick: this.pageClick
    };

    return (
      <main className="mp">
        <MyPointForm formFn={formFn} />
        <MyPointResult resultFn={resultFn} />
      </main>
    );
  }
}

MyPointList.contextTypes = {
  router: PropTypes.object.isRequired
};

MyPointList = connect(
  (state) => ({
    // common function call
    division: state.commons.division,

    // result of form selection
    date: state.commons.date,
    dateCheck: state.commons.dateCheck,
    statusStr: state.commons.statusStr,
    depth: state.commons.depth,
    text: state.commons.text,

    // result of meal function call
    type: state.usage.pointTypeList,
    status: state.usage.pointStatusList,
    list: state.usage.pointList
  }),
  (dispatch) => ({
    cmAPI: bindActionCreators(
      {
        changeExcel: cm_action.ChangeExcel
      },
      dispatch
    ),

    usageAPI: bindActionCreators(
      {
        pointType: usage_action.MyPointTypeList,
        pointStatus: usage_action.MyPointStatusList,
        pointList: usage_action.MyPointList
      },
      dispatch
    )
  })
)(MyPointList);

export default MyPointList;
