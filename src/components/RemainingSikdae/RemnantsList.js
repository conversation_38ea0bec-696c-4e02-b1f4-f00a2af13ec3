import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { LoadingBar } from 'components/Commons';
import { Popup } from 'semantic-ui-react';
import ReactTable from 'react-table';
import Infinite from 'react-infinite';
import update from 'react-addons-update';
import scroll from 'helpers/scroll';
import cm from 'helpers/commons';
import storage from 'helpers/storage';

const propTypes = {};

const defaultProps = {};

class RemnantsList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      elements: [],
      heightIdx: 0,
      containerHeight: 620,
      isInfiniteLoading: false,
      headers: [
        { header: 'ID', width: 120 },
        { header: '이름', width: 116 },
        { header: '사원번호', width: 128 },
        { header: '식대그룹', width: 128 },
        { header: '식대정책', width: 128 },
        { header: '잔여식대', width: 88, className: 'right', headerClassName: 'right' }
      ]
    };
    this.elementHeights = [];
  }

  componentWillReceiveProps(nextProps) {
    const { staffList } = nextProps;
    this.elementHeights = [];
    this.scrollEvent(staffList);
  }
  componentWillUpdate() {}
  scrollEvent = (list) => {
    if (list && list.data.user) {
      const { user } = list.data;
      this.setState({
        elements: this.buildElements(0, 100, user)
      });
    } else {
      this.setState({ elements: [] });
    }
  };

  handleInfiniteLoad = () => {
    // this.elementHeights = [];
    const { staffList } = this.props;
    const { elements, heightIdx, containerHeight } = this.state;
    const item = staffList ? staffList.data.user : null;

    let that = this;
    let elemLength = elements.length;
    let itemLength = item ? item.length : 0;
    const infiniteParm = scroll.event(item, elements, containerHeight, heightIdx, elemLength);

    if (elemLength < itemLength) {
      let data = this.buildElements(elemLength, elemLength + 100, item);
      this.setState({
        isInfiniteLoading: true,
        heightIdx: heightIdx + containerHeight
      });
      setTimeout(() => {
        that.setState({
          elements: that.state.elements.concat(data),
          heightIdx: infiniteParm.heightIdx,
          isInfiniteLoading: false
        });
      }, 500);
    } else {
      this.setState({
        isInfiniteLoading: false
      });
    }
  };

  elementInfiniteLoad = () => {
    return <LoadingBar />;
  };

  buildElements = (st, en, item) => {
    let elements = [];
    for (let i = st; i < item.length; i++) {
      const temp = this.listItem(item[i], st);
      if (temp) {
        elements.push(temp);
      }
    }
    return elements;
  };

  listItem = (item, st) => {
    const { headers, elementHeights } = this.state;
    let result = null;
    let rowHeight = 0;
    if (item) {
      const division = cm.trTdDivision(item.orgCode);
      let styles = [];
      headers.forEach((headerItem, index) => {
        styles.push({
          flex: `${headerItem.width} 0 auto`,
          width: headerItem.width,
          maxWidth: headerItem.width
        });
      });

      item.policy.forEach(() => {
        rowHeight += 40;
      });
      this.elementHeights.push(rowHeight);
      result = (
        <div className="rt-tr-group item-rt-tr-group" key={item.id}>
          <div className="rt-tr-group">
            <div className="rt-tr -odd">
              <div className="rt-td" style={styles[0]}>
                {item.signid}
              </div>
              <div className="rt-td" style={styles[1]}>
                {item.name}
              </div>
              {division.map((divi, i) => {
                return (
                  <div key={`divi${i}`} className="rt-td" style={{ flex: `100 0 auto `, width: 100 }}>
                    <Popup className="td-tooltip" inverted trigger={<span>{divi}</span>} content={divi} />
                  </div>
                );
              })}
              <div className="rt-td" style={styles[2]}>
                {item.comidnum}
              </div>
              <div className="rt-td" style={styles[3]}>
                {item.group.name}
              </div>
              <div className="rt-td" style={styles[4]}>
                {item.policy && item.policy.length > 0 ? item.policy[0].name : '-'}
              </div>
              <div className="rt-td right" style={styles[5]}>
                {item.policy && item.policy.length > 0 ? cm.numberComma(item.policy[0].amount) : 0}
              </div>
            </div>
            {item.policy.map((pitem, index) => {
              if (index === 0) {
                return null;
              }
              return (
                <div className="rt-tr-group" key={`policy${index}`}>
                  <div className="rt-tr -odd">
                    <div className="rt-td" style={styles[0]}></div>
                    <div className="rt-td" style={styles[1]}></div>
                    {division.map((divi, i) => {
                      return (
                        <div key={`divi-empty${i}`} className="rt-td" style={{ flex: `100 0 auto`, width: 100 }}></div>
                      );
                    })}
                    <div className="rt-td" style={styles[2]}></div>
                    <div className="rt-td" style={styles[3]}></div>
                    <div className="rt-td" style={styles[4]}>
                      {pitem.name}
                    </div>
                    <div className="rt-td right" style={styles[5]}>
                      {pitem.type === 'INFINITE' ? '∞' : cm.numberComma(pitem.amount)}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      );
      return result;
    } else {
      return null;
    }
  };
  tBodyFunc = (state) => {
    const { elements, containerHeight, isInfiniteLoading } = this.state;
    const dom = document.getElementsByClassName('item-rt-tr-group');
    try {
      return (
        <div className="rt-tbody" style={state.style}>
          <div className="rt-tr-group">
            <Infinite
              className="infinite-scroll"
              elementHeight={this.elementHeights}
              containerHeight={containerHeight}
              infiniteLoadBeginEdgeOffset={containerHeight - 50}
              onInfiniteLoad={this.handleInfiniteLoad}
              loadingSpinnerDelegate={this.elementInfiniteLoad()}
              isInfiniteLoading={isInfiniteLoading}
            >
              {elements}
            </Infinite>
          </div>
        </div>
      );
    } catch (e) {
    } finally {
    }
  };

  noDataProps = (state) => {
    const { staffList } = this.props;
    let dom = <div className="rt-noData">검색 결과가 없습니다.</div>;
    if (staffList && staffList.data.user && staffList.data.user.length > 0) {
      dom = <div className="rt-noData"></div>;
    }

    return dom;
  };

  render() {
    const { headers, isInfiniteLoading } = this.state;
    let header = cm.tableHeaderList(headers, 2, null, 'notMaxWidth');
    const columns = scroll.header(header, null);
    const { staffList } = this.props;

    return (
      <ReactTable
        className="react-table"
        noDataText={'검색 결과가 없습니다.'}
        columns={columns}
        sortable={false}
        resizable={false}
        showPagination={false}
        loading={isInfiniteLoading}
        TbodyComponent={this.tBodyFunc}
        NoDataComponent={this.noDataProps}
      />
    );
  }
}

RemnantsList.propTypes = propTypes;
RemnantsList.defaultProps = defaultProps;

RemnantsList = connect(
  (state) => ({
    staffList: state.commons.staff
  }),
  (dispatch) => ({})
)(RemnantsList);

export default RemnantsList;
