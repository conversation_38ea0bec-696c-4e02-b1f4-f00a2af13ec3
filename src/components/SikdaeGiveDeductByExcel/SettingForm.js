import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Field, reduxForm } from 'redux-form';
import { Button, Checkbox, Icon, Form, Label, Input } from 'semantic-ui-react';
import DatePicker from 'react-datepicker';
import update from 'react-addons-update';

import { FileInput, SingleDatePicker, Time } from 'components/Commons';

import * as sik_action from 'actions/sikdae';
import toastr from 'helpers/toastr';
import cm from 'helpers/commons';

class SettingForm extends Component {
  constructor() {
    super();
    this.state = {
      isLoading: false
    };
  }

  formSubmit = (event, newValue, previousValue) => {
    if (!newValue) return;
    const { FormFn } = this.props;

    let formData = new FormData();

    formData.append('file', newValue);
    FormFn.excelChecked(newValue, formData);
  };

  render() {
    const { isStartTime, enableStartTime } = this.props.FormFn;
    const { data, FormFn, auth, serviceType } = this.props;

    const isWelfare = serviceType === 'WELFARE';

    const title = isWelfare ? '' : '식대 ';
    const subTitle = isWelfare ? '지급 예약' : '시작시각 예약하기 ';
    const notice = isWelfare ? '복지포인트' : '식대';

    const mm = cm.dataNextMonth(1);
    const mm2 = cm.dataNextMonth(2);

    const year = new Date().getFullYear();

    return (
      <div className="deduct-excel-form">
        <div>
          <div className="left-txt">지급/차감 파일 업로드</div>
          <div className="right-content">
            <Field name="file" component={FileInput} type="file" onChange={this.formSubmit} />
          </div>
        </div>

        <div>
          <div className="left-txt">{title}유효기간 설정</div>
          <Form className="right-content">
            <div className="portion">
              <Form.Group>
                <Checkbox
                  label={`${title}${subTitle}`}
                  checked={auth.schedule ? true : data.timeDisable}
                  onChange={auth.schedule ? null : (e, data) => FormFn.timeIsShow(e, 'sDateOrTime', data)}
                  disabled={auth.schedule ? false : data.dateSetting}
                />
              </Form.Group>
              <Form.Group>
                <Input labelPosition="right" type="text">
                  <Label basic>
                    <Icon disabled name="calendar" style={{ margin: '8px 0' }} />
                  </Label>
                  <DatePicker
                    className={
                      auth.schedule
                        ? 'startDate'
                        : !data.timeDisable || data.dateSetting
                        ? 'dim startDate'
                        : 'startDate'
                    }
                    selected={data.startDate || data.dateSetting}
                    minDate={data.minDate}
                    dateFormat="YYYY-MM-DD"
                    disabled={auth.schedule ? false : !data.timeDisable}
                    onChange={(e) => FormFn.timeChange(e, 'sDate')}
                    customInput={<input style={{ width: 170 }} />}
                  />
                </Input>
                <Time
                  timeType="sTime"
                  placeholder={'HH:mm'}
                  isShowSecond={false}
                  isShowMinute={true}
                  isDim={auth.schedule ? false : !data.timeDisable || data.dateSetting}
                />
                <Icon name="minus" className="timepicker-bar" />
              </Form.Group>
            </div>
            <div className="portion">
              <p>{title}만료 시각</p>
              <Form.Group>
                <Input labelPosition="right" type="text">
                  <Label basic style={{ borderRight: 0 }}>
                    <Icon disabled name="calendar" style={{ margin: '8px 0' }} />
                  </Label>
                  <DatePicker
                    className={data.endDisable || data.dateSetting ? 'dim endDate' : 'endDate'}
                    selected={data.endDate}
                    minDate={data.minDate}
                    dateFormat="YYYY-MM-DD"
                    disabled={data.endDisable || data.dateSetting}
                    onChange={(e) => FormFn.timeChange(e, 'eDate')}
                    customInput={<input style={{ width: 170 }} />}
                  />
                </Input>
                {isWelfare ? (
                  <Button
                    inverted
                    color="black"
                    disabled={data.endDisable || data.dateSetting}
                    onClick={(e) => FormFn.timeChange(e, 'eEndDate3')}
                  >
                    {`${year}년 연말일까지`}
                  </Button>
                ) : (
                  <>
                    <Button
                      inverted
                      color="black"
                      disabled={data.endDisable || data.dateSetting}
                      onClick={(e) => FormFn.timeChange(e, 'eEndDate')}
                    >
                      {mm + '월 말일까지'}
                    </Button>
                    <Button
                      inverted
                      color="black"
                      disabled={data.endDisable || data.dateSetting}
                      onClick={(e) => FormFn.timeChange(e, 'eEndDate2')}
                    >
                      {mm2 + '월 말일까지'}
                    </Button>
                    <Button
                      inverted={!data.endDisable}
                      disabled={data.dateSetting}
                      color="black"
                      onClick={(e) => FormFn.timeIsShow(e, 'eEndDateDel')}
                    >
                      기간 제한 없음
                    </Button>
                  </>
                )}
              </Form.Group>
            </div>
            <div className="deduct-text">
              <div className="show-time">
                <Button
                  inverted={!data.dateSetting}
                  style={{ marginRight: '18px', width: 200 }}
                  color="green"
                  onClick={(e) => FormFn.dateSetting(e)}
                >
                  {data.dateSetting ? '재설정' : '기간 설정 완료'}
                </Button>
                {data.dateSetting
                  ? data.params.startDate +
                    ' ' +
                    (data.params.endDate
                      ? data.params.endDate + ' 24:00까지'
                      : data.endDisable
                      ? `${title}유효기간 없음`
                      : '')
                  : '* 유효기간을 설정해주세요.'}
              </div>
              <span>
                * 유효기간은 지급(+) {notice}만 적용되며, 차감(-) {notice}는 즉시 차감 처리됩니다.
              </span>
            </div>
          </Form>
        </div>
      </div>
    );
  }
}

SettingForm = connect(
  (state) => ({
    // excelCheck : state.sikdae.sikdae_excel_check,
    time: state.commons.time
    // excelUpload : state.sikdae.sikdae_excel_upload
  }),
  (dispatch) => ({
    sikAPI: bindActionCreators(
      {
        sikExcelCheck: sik_action.SikdaeExcelCheck
        // sikExcelUpload : sik_action.SikdaeExcelUpload
      },
      dispatch
    )
  })
)(SettingForm);

export default reduxForm({
  form: 'fileupload'
})(SettingForm);

//export default SettingForm;
