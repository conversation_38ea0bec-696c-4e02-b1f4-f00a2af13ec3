import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { LoadingBar, TableForm, Pagination } from 'components/Commons';
import GivenDtlModal from './Modal/GivenDtlModal';

import cm from 'helpers/commons';

class SikdaeGivenResult extends Component {
  constructor(props) {
    super(props);
  }

  render() {
    const { sikdaeList } = this.props,
      { page, pagerow, pageClick } = this.props.resultFn,
      { openModal } = this.props.modalFn;

    let pagination_page = 1,
      pagination_pagerow = 0,
      pagination_totalcount = 0;

    let loading = '',
      tabledata = {
        header: {
          main: [
            { data: '#', textAlign: 'left' },
            { data: '상태', textAlign: 'left' },
            { data: '식대', textAlign: 'right' },
            { data: '식대 그룹', textAlign: 'left' },
            { data: '식대 정책', textAlign: 'left' },
            { data: '신청일자', textAlign: 'left' },
            { data: '지급/차감일자', textAlign: 'left' },
            { data: '총 인원', textAlign: 'right' },
            { data: '성공', textAlign: 'right' },
            { data: '실패', textAlign: 'right' },
            { data: '신청자', textAlign: 'left' },
            { data: '사유', textAlign: 'left' }
          ]
        },
        body: {
          message: '식대 지급/차감 내역이 존재하지 않습니다.',
          main: null
        }
      };

    if (sikdaeList) {
      if (sikdaeList.data) {
        const { paging, task } = sikdaeList.data;

        if (paging) {
          pagination_page = paging.page;
          pagination_pagerow = paging.pagerow;
          pagination_totalcount = paging.totalcount;
        }

        if (task && task.length > 0) {
          tabledata.body.main = task.map((item) => {
            let statusColor = 'black',
              price = '-',
              priceColor = 'black',
              executeDate = item.date.excute
                ? cm.yyyyMMdd(new Date(item.date.excute)) + ' ' + cm.hhmmss(new Date(item.date.excute))
                : '',
              failColor = 'black',
              causeColor = 'black';
            //상태
            if (
              ['CANCEL', 'PLUS_SOME_FAIL', 'PLUS_ALL_FAIL', 'MINUS_SOME_FAIL', 'MINUS_ALL_FAIL'].indexOf(
                item.status.value
              ) > -1
            ) {
              statusColor = 'red';
            } else if ('RESERVE' === item.status.value) {
              statusColor = 'orange';
            }
            //금액, 사유
            if (['GROUP_RESET', 'WITHRAW_RESET', 'POLICY_RESET'].indexOf(item.status.value) > -1) {
              //item.amount === 0
              price = '초기화';
              priceColor = '#e20b44';
              causeColor = '#e20b44';
            } else if (item.amount >= 0) {
              price = cm.numberComma(item.amount);
            } else {
              price = cm.convertMinusNumber(item.amount);
              priceColor = 'red';
            }
            //실패
            if (item.count.fail > 0) {
              failColor = '#e20b44';
            }
            //지급예약 취소 사유, 지급/차감 시각
            if (['CANCEL'].indexOf(item.status.value) > -1) {
              causeColor = '#e20b44';
              executeDate = '-';
            }

            let obj = {
              clickFunc: openModal,
              clickData: item.id,
              row: [
                {
                  data: item.idx,
                  textAlign: 'left'
                },
                {
                  data: item.status.text,
                  textAlign: 'left',
                  fontColor: statusColor
                },
                {
                  data: price,
                  textAlign: 'right',
                  fontColor: priceColor
                },
                {
                  data: item.group.name,
                  textAlign: 'left'
                },
                {
                  data: item.policy.name,
                  textAlign: 'left'
                },
                {
                  data: cm.yyyyMMdd(new Date(item.date.regist)) + ' ' + cm.hhmmss(new Date(item.date.regist)),
                  textAlign: 'left'
                },
                {
                  data: executeDate,
                  textAlign: 'left'
                },
                {
                  data: cm.numberComma(item.count.total),
                  textAlign: 'right'
                },
                {
                  data: item.count.success ? cm.numberComma(item.count.success) : '-',
                  textAlign: 'right'
                },
                {
                  data: item.count.fail ? cm.numberComma(item.count.fail) : '-',
                  textAlign: 'right',
                  fontColor: failColor
                },
                {
                  data: item.user.name,
                  textAlign: 'left'
                },
                {
                  data: item.cause.text,
                  textAlign: 'left',
                  fontColor: causeColor,
                  isTooltip: true
                }
              ]
            };
            return obj;
          });
        } else {
          if (sikdaeList.response) {
            tabledata.body.message = sikdaeList.response.data.message;
          }
        }
      } else {
        loading = <LoadingBar />;
      }
    }

    return (
      <div>
        <div className="table-box">
          <TableForm selectable="true" tabledata={tabledata} />
        </div>

        <div className="table-paging">
          <Pagination
            page={pagination_page}
            pageRow={pagination_pagerow}
            pageBlock={10}
            totalCount={pagination_totalcount}
            pageClick={pageClick}
          />
        </div>

        <GivenDtlModal modalFn={this.props.modalFn} />
      </div>
    );
  }
}

SikdaeGivenResult = connect((state) => ({
  sikdaeList: state.sikdae.sikdaeGivenList
}))(SikdaeGivenResult);

export default SikdaeGivenResult;
