import React, { Component } from 'react';
import { connect } from 'react-redux';
import { <PERSON><PERSON>, Grid, <PERSON>, Header, Button } from 'semantic-ui-react';

import { LoadingBar, TableForm, Pagination } from 'components/Commons';

import * as sik_action from 'actions/sikdae';

import cm from 'helpers/commons';

class GivenDtlModal extends Component {
  constructor() {
    super();
  }

  render() {
    const { sikdaeDetail: detail, welfareDetail, welfare = false } = this.props;
    const title = welfare ? '복지포인트' : '식대';
    const sikdaeDetail = welfare ? { data: welfareDetail } : detail || {};
    const {
      modal,
      modalPage,
      modalPagerow,

      openModal,
      closeModal,
      detailPageClick,

      cancelSikdae,
      downloadExcel,
      isLoading
    } = this.props.modalFn;

    let header = [
      { data: '#', textAlign: 'left' },
      { data: '상태', textAlign: 'left' },
      { data: 'ID', textAlign: 'left' },
      { data: '이름', textAlign: 'left' },
      { data: title, textAlign: 'right' },
      { data: '상세 사유', textAlign: 'left' }
    ];

    let main = cm.tableHeaderList(header, 5, false, 'default');

    let p_page = modalPage,
      p_pagerow = modalPagerow,
      p_totalcount = 0,
      topDtl = '',
      bottomDtl = '',
      loading = '',
      reserveStatus = '',
      cancelReservationBtn = '',
      tabledata = {
        header: {
          main: main
        },
        body: {
          message: `${title} 상세내역이 존재하지 않습니다.`,
          main: null
        }
      };

    if (sikdaeDetail) {
      if (sikdaeDetail.data) {
        const { paging, task, taskuser } = sikdaeDetail.data;

        if (paging) {
          p_page = paging.page;
          p_pagerow = paging.pagerow;
          p_totalcount = paging.totalcount;
        }

        let price = cm.numberComma(task.amount) + ' 원(장)',
          status = task.status.text,
          execute = '-',
          successNfail = ' ',
          validDate = '-';

        //상태
        if (
          ['CANCEL', 'PLUS_SOME_FAIL', 'PLUS_ALL_FAIL', 'MINUS_SOME_FAIL', 'MINUS_ALL_FAIL'].indexOf(
            task.status.value
          ) > -1
        ) {
          status = <span className="txt-red">{task.status.text}</span>;
        }

        //지급/차감 시각
        if ('CANCEL' !== task.status.value) {
          execute = cm.yyyyMMdd(new Date(task.date.excute)) + ' ' + cm.hhmmss(new Date(task.date.excute));
        }

        //지급예약 케이스
        if ('RESERVE' === task.status.value) {
          cancelReservationBtn = (
            <Button basic color="red" className="reserve" onClick={cancelSikdae}>
              지급예약 취소
            </Button>
          );
          status = <span className="txt-orange">{task.status.text}</span>;
          reserveStatus = ' 처리대기';
        } else {
          //('RESERVE' !== task.status.value)
          successNfail = (
            <span>
              (성공: {cm.numberComma(task.count.success)} / 실패:{' '}
              <span className={task.count.fail ? 'txt-red' : ''}>{cm.numberComma(task.count.fail)}</span>)
            </span>
          );
        }

        //총액
        if (['GROUP_RESET', 'WITHRAW_RESET'].indexOf(task.status.value) > -1) {
          price = <span className="txt-red">{title} 초기화</span>;
        } else {
          if (task.amount > 0) {
            price = cm.numberComma(task.amount) + ` 원${welfare ? '' : '(장)'}`;
          } else if (task.amount < 0) {
            price = (
              <span>
                <span className="txt-red">{cm.convertMinusNumber(task.amount)}</span> {`원${welfare ? '' : '(장)'}`}
              </span>
            );
          } else {
            price = <span className="txt-red">{title} 초기화</span>;
          }
        }

        //유효기간
        if (['PLUS', 'PLUS_SOME_FAIL', 'PLUS_ALL_FAIL', 'RESERVE'].indexOf(task.status.value) > -1) {
          let text = `${title} 유효기간 없음`;

          if (task.date.expire) {
            text = cm.yyyyMMdd(new Date(task.date.expire)) + '  ' + cm.hhmmss(new Date(task.date.expire));
          }
          validDate = execute + ' ~ ' + text;
        }

        topDtl = (
          <Grid celled columns="equal" className="modal-summary">
            <Grid.Row>
              <Grid.Column width={2} className="summary-hd">
                {welfare ? '복지그룹' : '식대 구분'}
              </Grid.Column>
              <Grid.Column>
                {task.group.name} &gt;{' '}
                {`${task.policy.name}` +
                  (cm.isValidNumber(task.policy.maxHoldLimitAmount)
                    ? ` (최대보유한도: ${cm.numberComma(task.policy.maxHoldLimitAmount)})`
                    : '')}
              </Grid.Column>
              <Grid.Column width={2} className="summary-hd">
                {welfare ? '총 지급/차감한 복지포인트' : '총 식대'}
              </Grid.Column>
              <Grid.Column>{price}</Grid.Column>
            </Grid.Row>
            <Grid.Row>
              <Grid.Column width={2} className="summary-hd">
                {welfare ? '신청 일시' : '신청시각'}
              </Grid.Column>
              <Grid.Column>
                {cm.yyyyMMdd(new Date(task.date.regist))} {cm.hhmmss(new Date(task.date.regist))}
              </Grid.Column>
              <Grid.Column width={2} className="summary-hd">
                상태
              </Grid.Column>
              <Grid.Column>{status}</Grid.Column>
            </Grid.Row>
            <Grid.Row>
              <Grid.Column width={2} className="summary-hd">
                {welfare ? '진행(예정) 일시' : '지급/차감일자'}
              </Grid.Column>
              <Grid.Column>
                {execute} {reserveStatus}
              </Grid.Column>
              <Grid.Column width={2} className="summary-hd">
                지급/차감 인원
              </Grid.Column>
              <Grid.Column>
                총 {cm.numberComma(task.count.total)}명 {successNfail}
              </Grid.Column>
            </Grid.Row>
            <Grid.Row>
              <Grid.Column width={2} className="summary-hd">
                유효기간
              </Grid.Column>
              <Grid.Column>{validDate}</Grid.Column>
              <Grid.Column width={2} className="summary-hd">
                신청자
              </Grid.Column>
              <Grid.Column>{task.user.name}</Grid.Column>
            </Grid.Row>
          </Grid>
        );

        if (taskuser && taskuser.length > 0) {
          tabledata.body.main = taskuser.map((item) => {
            let statusText = '-',
              statusColor = 'black',
              amount = '-',
              amountColor = 'black',
              subCause = '',
              causeColor = 'black';

            if ('RESERVE' === task.status.value) {
              statusText = '지급 예약';
              statusColor = '#ff6f14';
            } else if (['GROUP_RESET', 'WITHRAW_RESET', 'POLICY_RESET'].indexOf(task.status.value) > -1) {
              statusText = task.status.text;
              statusColor = '#e20b44';
              causeColor = '#e20b44';
            } else {
              if (item.status.value === true) {
                statusText = item.status.text;
              } else {
                if (item.status.text) {
                  statusText = '실패';
                  subCause = '/ ' + item.status.text;
                  statusColor = '#e20b44';
                  causeColor = '#e20b44';
                } else {
                  statusText = '처리중';
                }
              }
            }

            //금액
            if (!item.amount) {
              amount = 0;
            } else if (item.amount >= 0) {
              amount = cm.numberComma(item.amount);
            } else {
              amount = cm.convertMinusNumber(item.amount);
              amountColor = '#e20b44';
            }

            let obj = {
              row: [
                {
                  data: item.idx,
                  textAlign: 'left'
                },
                {
                  data: statusText,
                  textAlign: 'left',
                  fontColor: statusColor
                },
                {
                  data: item.user.signid,
                  textAlign: 'left'
                },
                {
                  data: item.user.name,
                  textAlign: 'left'
                },
                {
                  data: amount,
                  textAlign: 'right',
                  fontColor: amountColor
                },
                {
                  data: item.cause + ' ' + subCause,
                  textAlign: 'left',
                  fontColor: causeColor,
                  isTooltip: true
                }
              ]
            };

            obj = cm.defalutTableStaffDivisionSet(obj.row, 5, item.user.orgCode);
            return obj;
          });
        }
      } else if (sikdaeDetail.response) {
        tabledata.body.message = sikdaeDetail.response.data.message;
      } else {
        loading = <LoadingBar />;
      }
    } else {
      loading = <LoadingBar />;
    }

    return (
      //dimmer={dimmer}
      <Modal className="modal-large sikdae-list-detail" open={modal} onClose={closeModal}>
        <Modal.Header>
          <span>{welfare ? '복지포인트 지급/차감 내역 상세' : '식대 상세내역'}</span>
          {cancelReservationBtn}
          <Button basic color="green" className="excel" onClick={downloadExcel} loading={isLoading}>
            엑셀 다운로드
          </Button>
        </Modal.Header>
        <Modal.Content>
          {loading}

          {topDtl}

          <div className="table-box">
            <TableForm tabledata={tabledata} />
          </div>

          <div className="table-paging">
            <Pagination
              page={p_page}
              pageRow={p_pagerow}
              pageBlock={10}
              totalCount={p_totalcount}
              pageClick={detailPageClick}
            />
          </div>
        </Modal.Content>
        <Modal.Actions>
          <Button positive content="닫기" onClick={closeModal} />
        </Modal.Actions>
      </Modal>
    );
  }
}

GivenDtlModal = connect((state) => ({
  sikdaeDetail: state.sikdae.sikdaeGivenDetail
}))(GivenDtlModal);

export default GivenDtlModal;
