import React, { useState, useEffect } from 'react';
import { Button, Checkbox, Input, Modal, Table } from 'semantic-ui-react';
import styled from 'styled-components';

const StyledModal = styled(Modal)`
  && {
    .header {
      padding: 16px;
      color: #212529;
      font-size: 22px;
      background: #f3f5f6;
      border-bottom: solid 1px #d7d8dc;
    }
    .content {
      padding: 16px;
    }
    .actions {
      display: flex;
      align-items: center;
      justify-content: end;
      padding: 16px;
      border-top: solid 1px #d7d8dc;
    }
    .desc {
      padding-bottom: 16px;
      font-size: 16px;
      color: #353c49;
    }
    .checkbox-group {
      display: flex;
      flex-direction: column;
      gap: 4px;
      padding-top: 16px;
      font-size: 12px;
    }
    .checkbox-group .require {
      color: #0bb656;
    }
    .checkbox-group .ui.checkbox label {
      color: #353c49;
    }
    .checkbox-group .ui.checkbox label:after {
      color: #ffffff !important;
      background: #0bb656;
      border-radius: 4px;
    }
    .conifrm-button {
      width: auto !important;
      min-width: 150px;
      margin-left: 8px;
      font-weight: 700;
      box-sizing: border-box;
    }
    .conifrm-button.disabled {
      color: #505052;
      background: #c8c9cc;
    }
    .back-button {
      height: 36px;
      font-size: 14px;
      font-weight: 700;
      color: #8e8e8e !important;
      border: solid 1px #d7d7d7;
      box-shadow: none;
    }
    .back-button:hover {
      color: #8e8e8e !important;
      border: solid 1px #c8c9cc;
      box-shadow: none;
    }
  }
`;

const StyledTable = styled(Table)`
  width: 100%;
  td {
    border: solid 1px #d7d8dc;
  }
  td:not(.thead) {
    padding: 0;
  }
  .info-title {
    width: 160px;
    background: #f3f5f6;
    text-align: center !important;
    font-weight: 600;
    color: #353c49;
    font-size: 12px;
  }
  .thead {
    width: 240px;
    color: #353c49;
    font-size: 12px;
    padding: 10px;
  }
  input,
  .disabled.input {
    padding: 6px 12px;
    width: 100%;
    max-height: 36px;
  }
  .disabled.input input {
    padding: 6px;
    font-size: 12px;
    background: #f2f4f6;
    color: #6d7582;
  }
  .form-input {
    display: flex;
    align-items: center;
    margin: 0px;
  }
  .form-input > div {
    flex: 1;
    padding: 6px 4px 6px 12px;
    max-height: 36px;
    input {
      padding: 6px;
      font-size: 12px;
      border: solid 1px #d7d8dc;
    }
  }
  .form-input button {
    width: 59px;
    min-height: 24px;
    padding-top: 0px;
    padding-bottom: 0px;
    margin-right: 12px;
    font-size: 12px;
  }
  .form-input button.disabled {
    color: #f2f4f6;
    background: #c8c9cc;
  }
`;

const TableRow = ({ label, value, rowSpan, title }) => {
  return (
    <Table.Row>
      {rowSpan ? (
        <Table.Cell className="info-title" rowSpan={rowSpan}>
          {title}
        </Table.Cell>
      ) : null}
      <Table.Cell className="thead">{label}</Table.Cell>
      <Table.Cell>
        <Input disabled value={value} />
      </Table.Cell>
    </Table.Row>
  );
};

const StatementCheckModal = ({ open, onClose, onConfirm, onUpdateDepositor, bizInfo }) => {
  const [checkedList, setCheckedList] = useState([]);
  const [depositor, setDepositor] = useState('');
  const [isUpdateDisabled, setUpdateDisabled] = useState(true);

  useEffect(() => {
    if (!open) {
      setCheckedList([]);
      setUpdateDisabled(true);
    }
    if (bizInfo) setDepositor(bizInfo.companyBankInfo.depositor);
  }, [open, bizInfo]);

  const handleChange = (event, target) => {
    const checkValue = target.name;
    if (target.checked) {
      setCheckedList((prev) => [...prev, checkValue]);
    } else if (!target.checked && checkedList.includes(checkValue)) {
      setCheckedList(checkedList.filter((item) => item !== checkValue));
    }
  };

  const handleDepositorChange = ({ target }) => {
    setUpdateDisabled(bizInfo && bizInfo.companyBankInfo.depositor === target.value);
    setDepositor(target.value);
  };

  const handleClick = () => {
    onUpdateDepositor(depositor);
  };

  if (!bizInfo) return null;
  return (
    <StyledModal open={open} onClose={onClose}>
      <Modal.Header>명세서 승인</Modal.Header>
      <Modal.Content>
        <div className="desc">내용 거래명세서 승인 전 아래 내용을 최종 확인 해주세요.</div>
        <StyledTable>
          <Table.Body>
            <TableRow label="상호(법인명)" value={`${bizInfo.bizName.value}`} rowSpan={6} title="사업자 정보" />
            <TableRow label="성명" value={`${bizInfo.chargeName.value}`} />
            <TableRow label="사업장 주소" value={`${bizInfo.address.value}`} />
            <TableRow label="업태" value={`${bizInfo.bizCondition.value}`} />
            <TableRow label="종목" value={`${bizInfo.bizType.value}`} />
            <TableRow
              label="이메일"
              value={`${bizInfo.email1.value} ${bizInfo.email2.value ? `/ ${bizInfo.email2.value}` : ''}`}
            />
            <TableRow
              label="입금 요청 금액"
              value={`${bizInfo.settleAmt.toLocaleString()}`}
              rowSpan={3}
              title="정산 정보"
            />
            <TableRow label="비고" value={`${bizInfo.taxRemark1.value}`} />
            <Table.Row>
              <Table.Cell className="thead">적요 (받는이에게 표기)</Table.Cell>
              <Table.Cell>
                <div className="form-input">
                  <Input onChange={handleDepositorChange} value={depositor} />
                  <Button color="green" onClick={handleClick} disabled={isUpdateDisabled}>
                    수정
                  </Button>
                </div>
              </Table.Cell>
            </Table.Row>
          </Table.Body>
        </StyledTable>
        <div className="checkbox-group">
          <div>
            <Checkbox
              name="A"
              onChange={handleChange}
              label="적요(받는이에게 표기) 위의 입금자명과 동일하게 입금함을 확인하였습니다."
            />
            <span className="require">*</span>
          </div>
          <div>
            <Checkbox
              name="B"
              onChange={handleChange}
              label={`
                입금계좌 : ${bizInfo.companyBankInfo.accountNo}(${bizInfo.companyBankInfo.bankName}) /
                예금주명 : ${bizInfo.companyBankInfo.accountHolder} 을 확인하였습니다.
              `}
            />
            <span className="require">*</span>
          </div>
          <div>
            <Checkbox
              name="C"
              onChange={handleChange}
              label="명세서를 승인 이후에는 수정/재발행이 불가에 동의합니다."
            />
            <span className="require">*</span>
          </div>
        </div>
      </Modal.Content>
      <Modal.Actions>
        <Button className="back-button" basic onClick={onClose}>
          돌아가기
        </Button>
        <Button className="conifrm-button" color="green" onClick={onConfirm} disabled={checkedList.length < 3}>
          명세서 승인하기
        </Button>
      </Modal.Actions>
    </StyledModal>
  );
};

export default StatementCheckModal;
