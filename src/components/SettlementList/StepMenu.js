import React, { Component } from 'react';

import { Step } from 'semantic-ui-react';
import { connect } from 'react-redux';
import Constants from 'components/SettlementList/Constants';

class StepMenu extends Component {
  renderProgressStep = (stepList, currentStep) => {
    const stepObj = stepList.map((item) => {
      let current = false;

      if (item.id === currentStep) {
        current = true;
      }

      return (
        <Step completed={current} active={current} key={item.id}>
          <Step.Content>
            <Step.Title>{item.name}</Step.Title>
          </Step.Content>
        </Step>
      );
    });

    return (
      <Step.Group ordered className="settlement" style={{ marginBottom: '10px' }}>
        {stepObj}
      </Step.Group>
    );
  };

  renderDetailStep = (stepList, currentStep) => {
    const stepObj = stepList.map((item) => {
      let current = false;

      if (item.id <= currentStep) {
        current = true;
      }

      return (
        <Step completed={current} key={item.id}>
          <Step.Content>
            <Step.Title>{item.name}</Step.Title>
          </Step.Content>
        </Step>
      );
    });

    return (
      <Step.Group ordered className="settlementDetail" style={{ marginTop: '0px', marginBottom: '10px' }}>
        {stepObj}
      </Step.Group>
    );
  };

  render() {
    const { stepProgress } = this.props;
    let progressStep;
    let detailStep;

    if (stepProgress && stepProgress.data) {
      const { companyProgressStatusList, currentProgressStatus } = stepProgress.data;
      const { companyProgressStatusDetailList, currentProgressDetailStatus } = stepProgress.data;

      progressStep = this.renderProgressStep(companyProgressStatusList, currentProgressStatus);

      if (currentProgressStatus !== Constants.PROGRESS_STATUS.COMPLETE) {
        // 정산확정 단계일 경우 상세 단계를 그리지 않음
        detailStep = this.renderDetailStep(companyProgressStatusDetailList, currentProgressDetailStatus);
      }
    }

    return (
      <div className="sixten wide column rendered-example">
        {progressStep}
        {detailStep}
      </div>
    );
  }
}

StepMenu = connect((state) => ({
  stepProgress: state.settlement.progressStatus
}))(StepMenu);

export default StepMenu;
