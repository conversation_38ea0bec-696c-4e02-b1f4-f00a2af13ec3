import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Button, Icon, Menu } from 'semantic-ui-react';

import { SikdaeGroupDtlModal } from './Modal';

import * as cm_action from 'actions/commons';

class GroupListBtn extends Component {
  constructor(props) {
    super(props);

    this.state = {
      open: false,
      name: ''
    };
  }

  //open 식대 그룹 상세보기 Modal
  showModal = (groupName) => {
    this.setState({
      open: true,
      name: groupName
    });
  };

  //close 식대 그룹 상세보기 Modal
  closeModal = () => {
    this.setState({ open: false });
  };

  //식대 그룹 정책
  sikdaePolicySel = (groupIdx, groupName) => {
    const { cmAPI } = this.props;

    try {
      this.showModal(groupName);
      cmAPI.sikdaePolicySel(groupIdx);
    } catch (e) {
      console.log('##### sikdaePolicySel  : ', e);
    }
  };

  render() {
    const { sikdaeGroup, sikdaeGroupClick, groupNum } = this.props.GroupListFn;

    // let buttonList = sikdaeGroup ?  sikdaeGroup.data.group.map((data, idx) => {
    //
    //   return (
    //     <Button.Group basic fluid key={data.idx}>
    //       <Button
    //         basic color='teal'
    //         id={data.idx === groupNum ? "btn-active" : ""}
    //         className="sGroup-btn"
    //         onClick={(e) => {sikdaeGroupClick(data.idx, data.name)}}
    //       >{data.name}</Button>
    //       <Button
    //         color='teal'
    //         id="icon-btn"
    //         icon="unhide"
    //         onClick={() => {this.sikdaePolicySel(data.idx, data.name)}} />
    //     </Button.Group>
    //   )
    //
    // }) : "";

    let buttonList = sikdaeGroup
      ? sikdaeGroup.data.group.map((data, i) => {
          return (
            <Menu.Item
              key={data.idx}
              active={data.idx === groupNum ? true : false}
              onClick={(e) => {
                sikdaeGroupClick(data.idx, data.name);
              }}
            >
              <Icon
                name="info circle"
                size="large"
                color="grey"
                onClick={() => {
                  this.sikdaePolicySel(data.idx, data.name);
                }}
              />
              {data.name}
            </Menu.Item>
          );
        })
      : '';

    const varModal = {
      close: this.closeModal,
      open: this.state.open,
      name: this.state.name
    };

    return (
      <div>
        <div className="step-txt">Step1. 식대그룹 설정</div>

        <div className="list-scroll">
          <Menu vertical fluid>
            {sikdaeGroup ? buttonList : ''}
          </Menu>
        </div>

        <SikdaeGroupDtlModal varModal={varModal} />
      </div>
    );
  }
}

GroupListBtn = connect(
  (state) => ({}),
  (dispatch) => ({
    cmAPI: bindActionCreators(
      {
        sikdaePolicySel: cm_action.SikdaePolicyList
      },
      dispatch
    )
  })
)(GroupListBtn);

export default GroupListBtn;
