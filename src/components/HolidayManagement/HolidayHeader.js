import React from 'react';
import PropTypes from 'prop-types';
import { Button, Dropdown, Form, Grid } from 'semantic-ui-react';
import moment from 'moment-timezone';
import cm from 'helpers/commons';
import styled from 'styled-components';

moment.tz.setDefault('Asia/Seoul');

const propTypes = {
  value: PropTypes.string,
  options: PropTypes.arrayOf(PropTypes.any),
  header: PropTypes.oneOf(['history', 'modify']).isRequired
};
const defaultProps = {
  value: '',
  options: []
};

const HolidayHeader = ({ value, header, options, onSearch, onMovePage, onAdd }) => {
  const handleChange = (e, data) => {
    onSearch(data.value);
  };
  const auth = cm.authGroupSet(null);

  return (
    <StyledForm className="top-form">
      <Grid>
        <Grid.Row className="form-row">
          <Grid.Column width={16}>
            <ul>
              <li>* 식대 지급 및 사용과 관련하여, 공휴일로 처리해야 하는 날짜를 관리하는 기능입니다.</li>
              <li>
                * 기본 국가 공휴일은 현대벤디스에서 관리하며, 현대벤디스에서 설정한 국가 공휴일의 활성 여부를 제어 할 수
                있습니다.
              </li>
            </ul>
          </Grid.Column>
        </Grid.Row>
        {header === 'history' ? (
          <StyledGridRow>
            <Grid.Column width={14}>
              <StyledFormGroup>
                <Form.Field inline className="com-sikdae-group">
                  <Dropdown value={value} selection options={options} onChange={handleChange} />
                </Form.Field>
              </StyledFormGroup>
            </Grid.Column>
            <Grid.Column width={2} floated="right">
              {auth.all ? (
                <Button inverted color="green" onClick={onMovePage}>
                  수정
                </Button>
              ) : null}
            </Grid.Column>
          </StyledGridRow>
        ) : (
          <StyledGridRow>
            <Grid.Column width={1} textAlign="left" verticalAlign="middle">
              {value} 년
            </Grid.Column>
            <Grid.Column width={6} floated="right" verticalAlign="middle">
              <Button inverted color="green" onClick={() => onAdd()}>
                공휴일 추가
              </Button>
            </Grid.Column>
          </StyledGridRow>
        )}
      </Grid>
    </StyledForm>
  );
};

const StyledForm = styled(Form)`
  &&&& {
    margin-bottom: 10px;
  }
`;

const StyledFormGroup = styled(Form.Group)`
  &&&& {
    margin-top: 10px;
    margin-bottom: 0;
  }
`;

const StyledGridRow = styled(Grid.Row)`
  &&&& {
    padding-bottom: 0;
  }
`;

HolidayHeader.propTypes = propTypes;
HolidayHeader.defaultProps = defaultProps;

export default HolidayHeader;
