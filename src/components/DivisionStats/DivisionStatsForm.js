import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Form, Grid, Button } from 'semantic-ui-react';

import { DateType,
         DateForm,
         Division } from 'components/Commons';

class DivisionStatsForm extends Component {

  render() {

    const { division,
            submitForm } = this.props.formFn;

    return(
      <Form className="top-form">
        <Grid>
          <Grid.Row className="form-row">
            <Grid.Column width={14}>
              <Form.Group>
                <DateType />
                <DateForm />
              </Form.Group>
            </Grid.Column>
            <Grid.Column width={2} className="right">
              <Button
                color="black" inverted
                content='조회'
                onClick={submitForm}
              />
            </Grid.Column>
          </Grid.Row>

          <Grid.Row className="form-row">
            <Grid.Column width={16}>
              <Division division={division} />
            </Grid.Column>
          </Grid.Row>
        </Grid>
      </Form>
    );
  }

}

export default DivisionStatsForm;
