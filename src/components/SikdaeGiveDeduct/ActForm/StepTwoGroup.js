import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { Menu } from 'semantic-ui-react';

const propTypes = {
};

const defaultProps = {
};

class StepTwoGroup extends Component {

  constructor(props) {
    super(props);
  }

  render() {

    const { sikdaeGroup, data, fn, groupNum } = this.props;

    let actGroup = data.groupNum ? data.groupNum : groupNum;
    let buttonList = sikdaeGroup ? sikdaeGroup.data.group.map((item, i) => {

      let count = 0;

      data.staffCheckedData.forEach((data) => {

        if(data.group.idx === item.idx) {

          if(count === 0) {
          }
          count += 1;
        }
      })

      if(count !== 0) {
        return (

          <Menu.Item
            name={item.name}
            active={item.idx === actGroup ? true : false}
            key={item.idx}
            onClick={ (e) => fn.stepTwoSikdaeGroupClick(item.idx, item.name) }>
            {item.name + "(" + count + ")"}
          </Menu.Item>
        )
      }else {
        return null;
      }

    }) : null

    return(
      <div>
        <Menu vertical className="vendysLeftMenu group" fluid>
          <Menu.Item className="top" name='title'>
            식대그룹
          </Menu.Item>
          {
            sikdaeGroup
              ? buttonList
              : ""
          }
        </Menu>
      </div>
    );
  }
}

StepTwoGroup.propTypes = propTypes;
StepTwoGroup.defaultProps = defaultProps;

StepTwoGroup = connect(

  state => ({
    sikdaeGroup     : state.commons.sikdaeGroup
  })

)(StepTwoGroup);

export default StepTwoGroup;
