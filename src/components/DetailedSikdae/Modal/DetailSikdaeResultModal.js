import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Modal, Grid, Table, Button } from 'semantic-ui-react';

import { LoadingBar } from 'components/Commons';

import cm from 'helpers/commons';
import storage from 'helpers/storage';

class DetailSikdaeResultModal extends Component {
  makePaymentRow = (item, idx) => {
    const depth = storage.get('pastDepth');
    let groupLength = 1;

    if (item.group) {
      groupLength = item.group.policy.length;
    }
    const rowSpan = groupLength;

    const list = [];
    let totalprice = 0;
    for (let i = 0; i < rowSpan; i++) {
      let name = '-';
      let amount = '-';
      if (item.group) {
        const policy = item.group.policy[i];
        name = policy.name ? policy.name : '-';
        amount = policy.amount ? cm.numberComma(item.group.policy[i].amount) : '-';
        totalprice += item.group.policy[i].amount;
      } else {
        name = '-';
        amount = '-';
      }

      if (i === 0) {
        const cellRow = [
          <Table.Cell key="sginid">{item.signid}</Table.Cell>,
          <Table.Cell key="name">{item.name}</Table.Cell>,
          <Table.Cell key="group">{item.group ? item.group.name : '-'}</Table.Cell>,
          <Table.Cell key="name1">{name}</Table.Cell>,
          <Table.Cell key="amount" textAlign="right">
            {amount}
          </Table.Cell>
        ];

        let divisionNavi = cm.divisionNavi(item.orgCode, true);

        if (divisionNavi) {
          divisionNavi = divisionNavi.split('>');
        }

        for (let l = 1; l <= depth; l++) {
          let divisionName = '';

          if (divisionNavi && divisionNavi[l - 1]) {
            divisionName = divisionNavi[l - 1];
          }

          cellRow.splice(1 + l, 0, <Table.Cell key={`division${l}`}>{divisionName}</Table.Cell>);
        }

        list.push(
          <Table.Row key={`${item.signid}_${i}`}>
            {cellRow.map((data) => {
              return data;
            })}
          </Table.Row>
        );
      } else {
        list.push(
          <Table.Row key={`${item.signid}_${i}`}>
            <Table.Cell colSpan={3 + depth} />
            <Table.Cell>{name}</Table.Cell>
            <Table.Cell textAlign="right">{amount}</Table.Cell>
          </Table.Row>
        );
      }
    }

    list.push(
      <Table.Row key={idx} className="total-price">
        <Table.Cell colSpan={5 + depth} textAlign="right">
          {cm.numberComma(totalprice)}
        </Table.Cell>
      </Table.Row>
    );
    return list;
  };

  render() {
    const { listDetail, modalFn } = this.props;
    const { modal, closeModal } = modalFn;

    const depth = storage.get('pastDepth');

    let loading = '';
    let top = '';
    let middle = '';

    if (listDetail) {
      if (listDetail.data) {
        const { room, store, user } = listDetail.data;
        let roomLeader = '-';
        let roomMember = '-';

        middle =
          user && user.length > 0 ? (
            user.map((item, idx) => {
              if (item.paytype !== 1) {
                // 방장

                if (roomMember !== '-') {
                  roomMember += `, ${item.name}`;
                } else {
                  roomMember = item.name;
                }

                if (item.signid) {
                  roomMember += ` (${item.signid})`;
                }
              } else if (item.signid) {
                roomLeader = `${item.name} (${item.signid})`;
              } else {
                roomLeader = item.name;
              }

              return this.makePaymentRow(item, idx);
            })
          ) : (
            <Table.Row>
              <Table.Cell colSpan={5 + depth}>결제 상세정보가 존재하지 않습니다.</Table.Cell>
            </Table.Row>
          );

        middle.push(
          <Table.Row key="final_row" className="final_row">
            <Table.Cell colSpan={4 + depth}>결제총액</Table.Cell>
            <Table.Cell textAlign="right">{cm.numberComma(room.price.pay)}</Table.Cell>
          </Table.Row>
        );

        const payText = room.pay && room.pay.text ? room.pay.text : '-';
        top = (
          <Grid celled columns="equal" className="modal-summary">
            <Grid.Row>
              <Grid.Column width={2} className="summary-hd">
                결제번호
              </Grid.Column>
              <Grid.Column>{room.id}</Grid.Column>
              <Grid.Column width={2} className="summary-hd">
                가맹점
              </Grid.Column>
              <Grid.Column>{store.name}</Grid.Column>
            </Grid.Row>

            <Grid.Row>
              <Grid.Column width={2} className="summary-hd">
                결제 날짜
              </Grid.Column>
              <Grid.Column>
                {cm.yyyyMMdd(new Date(room.usedate))} {cm.hhmmss(new Date(room.usedate))}
              </Grid.Column>
              <Grid.Column width={2} className="summary-hd">
                결제형태
              </Grid.Column>
              <Grid.Column>{payText}</Grid.Column>
            </Grid.Row>
            <Grid.Row>
              <Grid.Column width={2} className="summary-hd">
                상태
              </Grid.Column>
              <Grid.Column>{room.addons.status}</Grid.Column>
              <Grid.Column width={2} className="summary-hd">
                공급형태
              </Grid.Column>
              <Grid.Column>{room.addons.vendor}</Grid.Column>
            </Grid.Row>
            <Grid.Row>
              <Grid.Column width={2} className="summary-hd">
                결제자
              </Grid.Column>
              <Grid.Column>{roomLeader}</Grid.Column>
            </Grid.Row>
            <Grid.Row>
              <Grid.Column width={2} className="summary-hd">
                함께결제자
              </Grid.Column>
              <Grid.Column>{roomMember}</Grid.Column>
            </Grid.Row>
            <Grid.Row>
              <Grid.Column width={2} className="summary-hd">
                회사 식대
              </Grid.Column>
              <Grid.Column>{cm.numberComma(room.price.pay)}</Grid.Column>
              <Grid.Column width={2} className="summary-hd">
                정산 금액
              </Grid.Column>
              <Grid.Column>{cm.numberComma(room.price.com)}</Grid.Column>
            </Grid.Row>
          </Grid>
        );
      } else if (listDetail.response) {
        loading = listDetail.response.data.message;
      }
    } else {
      loading = <LoadingBar />;
    }

    const header = [
      <Table.HeaderCell key="id">ID</Table.HeaderCell>,
      <Table.HeaderCell key="user">사용자</Table.HeaderCell>,
      <Table.HeaderCell key="group">식대그룹</Table.HeaderCell>,
      <Table.HeaderCell key="policy">식대정책</Table.HeaderCell>,
      <Table.HeaderCell key="money" textAlign="right">
        회사식대 결제금액
      </Table.HeaderCell>
    ];

    for (let i = 1; i <= depth; i++) {
      header.splice(1 + i, 0, <Table.HeaderCell key={`division${i}`}>{`부서${i}`}</Table.HeaderCell>);
    }

    return (
      <Modal className="modal-large" open={modal} onClose={closeModal}>
        <Modal.Header>식대결제 상세정보</Modal.Header>

        <Modal.Content>
          {loading}
          {top}
          <br />
          <div>
            <div className="modal-tb-tit">결제 상세정보</div>
            <Table className="pay-detail">
              <Table.Header>
                <Table.Row>
                  {header.map((data) => {
                    return data;
                  })}
                </Table.Row>
              </Table.Header>
              <Table.Body>{middle}</Table.Body>
            </Table>
          </div>
        </Modal.Content>
        <Modal.Actions>
          <Button positive content="확인" onClick={closeModal} />
        </Modal.Actions>
      </Modal>
    );
  }
}

export default connect((state) => ({
  listDetail: state.usage.listDetail
}))(DetailSikdaeResultModal);
