import React, { Component } from 'react';
import { Grid, Table, Header, Icon } from 'semantic-ui-react';

import { LoadingBar, TableForm, Pagination } from 'components/Commons';

import cm from 'helpers/commons';

class MyPointResult extends Component {
  render() {
    const { list, page, pageRow, pageClick } = this.props.resultFn;

    const header = [
      { data: '날짜', textAlign: 'left' },
      { data: 'ID', textAlign: 'left' },
      { data: '이름', textAlign: 'left' },
      { data: '변동금액', textAlign: 'right' },
      { data: '잔액', textAlign: 'right' },
      { data: '사유', textAlign: 'left' },
      { data: '내역', textAlign: 'left' },
      { data: '소멸예정일', textAlign: 'left' }
    ];

    const main = cm.tableHeaderList(header, 3, false, 'default');

    let p_page = page;
    let p_pagerow = pageRow;
    let p_totalcount = 0;
    let pointList;
    let loading = '';
    const tabledata = {
      header: {
        main
      },
      body: {
        message: '대장 포인트 내역이 존재하지 않습니다.',
        main: null
      }
    };

    if (list) {
      if (list.data) {
        const { paging, mypoint } = list.data;

        if (mypoint && mypoint.length > 0) {
          p_page = paging.page;
          p_pagerow = paging.pagerow;
          p_totalcount = paging.totalcount;

          tabledata.body.main = mypoint.map((item, idx) => {
            const date = `${cm.yyyyMMdd(new Date(item.date.change))} ${cm.hhmmss(new Date(item.date.change))}`;

            let variableText = cm.numberComma(item.amount.variable);
            let variableColor = 'black';
            let expired = '기한없음';

            // 변동 금액
            if (item.amount.variable < 0) {
              variableText = cm.convertMinusNumber(item.amount.variable);
              variableColor = '#e20b44';
            }
            // 소멸예정일
            if (item.date.extinct) {
              expired = `${cm.yyyyMMdd(new Date(item.date.extinct))} ${cm.hhmmss(new Date(item.date.extinct))}`;
            }

            let obj = {
              row: [
                {
                  data: date,
                  textAlign: 'left'
                },
                {
                  data: item.user.signid,
                  textAlign: 'left'
                },
                {
                  data: item.user.name,
                  textAlign: 'left'
                },
                {
                  data: variableText,
                  textAlign: 'right',
                  fontColor: variableColor
                },
                {
                  data: cm.numberComma(item.amount.balance),
                  textAlign: 'right'
                },
                {
                  data: item.cause,
                  textAlign: 'left'
                },
                {
                  data: item.status.text,
                  textAlign: 'left'
                },
                {
                  data: expired,
                  textAlign: 'left'
                }
              ]
            };

            obj = cm.defalutTableStaffDivisionSet(obj.row, 3, item.user.orgCode);

            return obj;
          });
        }
      } else if (sikdaeList.response) {
        tabledata.body.message = sikdaeList.response.data.message;
      }
    } else {
      loading = <LoadingBar />;
    }

    return (
      <div>
        {loading}

        <div className="table-box">
          <TableForm tabledata={tabledata} />
        </div>

        <div className="table-paging">
          <Pagination
            page={p_page}
            pageRow={p_pagerow}
            pageBlock={10}
            totalCount={p_totalcount}
            pageClick={pageClick}
          />
        </div>
      </div>
    );
  }
}

export default MyPointResult;
