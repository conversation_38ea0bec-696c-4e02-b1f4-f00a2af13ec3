import React, { Component } from 'react';
import PropTypes from 'prop-types';
import BarcodeBody from 'react-barcode';
import html2canvas from 'html2canvas';
import styled from 'styled-components';
import { Grid, Button, Modal, Image } from 'semantic-ui-react';
import logo from 'images/loginLogo.png';

const propTypes = {};
const defaultProps = {};

const UserBarcode = ({ modalFn, data }) => {
  const { open, close, download } = modalFn;

  return (
    <Modal className="modal-small" open={open} onClose={() => close('취소')}>
      <Modal.Header>바코드 출력</Modal.Header>
      <StyledModalContent>
        <StyledGridRowBody id="printBarcode">
          <StyledGridRowTitle textAlign="center">
            <Grid.Column>
              {data.name} - {data.group}
            </Grid.Column>
          </StyledGridRowTitle>
          <Grid.Row textAlign="center">
            <Grid.Row id="captureBarcode" style={{ margin: '0 auto' }}>
              {data.barcode ? (
                <BarcodeBody
                  value={data.barcode}
                  displayValue={false}
                  width={4}
                  height={160}
                  font="monospace"
                />
              ) : (
                <span>바코드가 없습니다.</span>
              )}
            </Grid.Row>
          </Grid.Row>
          <StyledGridRowActions>
            <StyledGridColumn
              floated="left"
              width={5}
              style={{ marginLeft: 40 }}
            >
              <Image src={logo} height="44" alt="식권대장" />
            </StyledGridColumn>
            {/* <Grid.Column floated="right" width={5} style={{ marginRight: 40 }}>
              회사 로고가 있으면 노출
            </Grid.Column> */}
          </StyledGridRowActions>
        </StyledGridRowBody>
      </StyledModalContent>
      <Modal.Actions>
        <Button
          className="cancel"
          inverted
          color="grey"
          onClick={() => close('취소')}
        >
          취소
        </Button>
        <Button color="green" onClick={download} content="다운로드" />
      </Modal.Actions>
    </Modal>
  );
};

const StyledModalContent = styled(Modal.Content)`
  &&&& {
    padding: 0;
  }
`;

const StyledGridRowBody = styled(Grid)`
  &&&& {
    width: 580px;
    height: 352px;
    padding: 1.5rem 0;
    margin: 0;
  }
`;

const StyledGridRowTitle = styled(Grid.Row)`
  &&&& {
    font-weight: bold;
    font-size: 1.7rem;
    padding-bottom: 0;
  }
`;

const StyledGridRowActions = styled(Grid.Row)`
  &&&& {
    padding: 0;
  }
`;
const StyledGridColumn = styled(Grid.Column)`
  &&&& {
    padding: 0;
  }
`;

UserBarcode.propTypes = propTypes;
UserBarcode.defaultProps = defaultProps;

export default UserBarcode;
