import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Input, Button, Form } from 'semantic-ui-react';

import * as cm_action from 'actions/commons';

const propTypes = {};

const defaultProps = {};

class SearchInputBox extends Component {
  constructor(props) {
    super(props);
  }

  componentWillMount() {
    this.changeText(null, { value: null });
  }

  changeText = async (data) => {
    const { cmAPI } = this.props;

    await cmAPI.changeText({
      type: 'text',
      name: 'value',
      value: data
    });
  };

  render() {
    const { styles, handleSubmit, handleEntSubmit, placeHolder, isInline } = this.props;
    const holder = placeHolder ? placeHolder : 'Search...';

    return (
      <Form.Field inline className={isInline ? 'inline' : null}>
        <Input
          className="search-input"
          placeholder={holder}
          style={styles}
          onChange={(e) => this.changeText(e.target.value)}
          onKeyPress={handleEntSubmit}
        >
          <input />
          <Button type="button" icon="search" style={{ padding: '0px 25px 0px 8px' }} onClick={handleSubmit} />
        </Input>
      </Form.Field>
    );
  }
}

SearchInputBox.propTypes = propTypes;
SearchInputBox.defaultProps = defaultProps;

SearchInputBox = connect(
  (state) => ({
    text: state.commons.text
  }),
  (dispatch) => ({
    cmAPI: bindActionCreators(
      {
        changeText: cm_action.ReduxDataSet
      },
      dispatch
    )
  })
)(SearchInputBox);

export default SearchInputBox;
