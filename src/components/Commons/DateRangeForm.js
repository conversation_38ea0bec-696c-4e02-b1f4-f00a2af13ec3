import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import DatePicker from 'react-datepicker';
import moment from 'moment-timezone';
moment.tz.setDefault('Asia/Seoul');
import 'react-datepicker/dist/react-datepicker.css';

import * as cmAPI_Action from 'actions/commons';
import cm from 'helpers/commons';
import toastr from 'helpers/toastr';

import { Dropdown, Form, Input, Label, Icon, Button } from 'semantic-ui-react';

class DateRangeForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      ...this.getDateRangeByType(),
      month: moment().format('MM'),
      sDateShow: false,
      eDadeShow: false
    };
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.loadData !== nextProps.loadData) {
      if (
        nextProps.loadData &&
        nextProps.loadData.constructor === Object &&
        Object.entries(nextProps.loadData).length === 0
      ) {
        this.setState({
          ...this.getDateRangeByType()
        });
      } else {
        this.setState({
          startDate: moment(nextProps.loadData.start),
          endDate: moment(nextProps.loadData.end)
        });
      }
    }
  }

  componentWillMount() {
    const { startDate, endDate } = this.state;
    this.handleChange(startDate, 'sDate', false);
    this.handleChange(endDate, 'eDate', false);
  }

  handleMonth = async (e, { value, id }) => {
    e.preventDefault();
    this.setState({ month: value });

    if (id === 'today') {
      await this.setState({
        startDate: moment(),
        endDate: moment()
      });
    } else {
      if (value !== moment().format('MM')) {
        //last months

        await this.setState({
          startDate: moment()
            .subtract(id, 'month')
            .startOf('month'),
          endDate: moment()
            .subtract(id, 'month')
            .endOf('month')
        });
      } else {
        // this month

        await this.setState({
          startDate: moment().startOf('month'),
          endDate: moment()
        });
      }
    }

    const { startDate, endDate } = this.state;
    this.handleChange(startDate, 'sDate', false);
    this.handleChange(endDate, 'eDate', false);
  };

  handleChange = async (date, type, isActive) => {
    const { changeData, loadData } = this.props;
    if (type === 'sDate') {
      await this.setState({
        startDate: date,
        sDateShow: false
      });
    } else if (type === 'eDate') {
      await this.setState({
        endDate: date,
        eDateShow: false
      });
    }

    const { startDate, endDate, sDateShow, eDateShow } = this.state;
    const result = cm.dateCheck(startDate, endDate, true); //날짜 체크

    changeData(this.state);
    if (!result.isState) {
      if (!sDateShow && !eDateShow) {
        toastr('top-right', 'warning', result.message);
      }

      // if(type === "sDate"){
      //   let tempDate = new Date(date);
      //   // this.handleChange(moment(tempDate).add(92, "days"), "eDate", false);
      // }else if(type === "eDate") {
      //   let tempDate = new Date(date);
      //   // this.handleChange(moment(tempDate).subtract(92, "days"), "sDate", false);
      // }
      changeData(this.state);
      return;
    }
  };

  getDateRangeByType() {
    const { periodType } = this.props;
    let range = {
      startDate: moment(),
      endDate: moment()
    };
    switch (periodType) {
      case 'month':
        range = {
          startDate: moment().startOf(periodType),
          endDate: moment().endOf(periodType)
        };
        break;
      case 'year':
        range = {
          startDate: moment().startOf(periodType),
          endDate: moment().endOf(periodType)
        };
        break;
      default:
        // day
        break;
    }
    return range;
  }

  calendarOpen = (type) => {
    this.setState({
      sDateShow: true,
      eDateShow: true,
      month: null
    });
    if (type === 'start') {
      // $('#end').click();
    } else {
      // $('#start').click();
    }
  };

  clickOutSide = (e) => {
    const { startDate, endDate } = this.state;
    const result = cm.dateCheck(startDate, endDate, true); //날짜 체크
    let path = e.target.className.split('__');
    if (path[0] !== 'react-datepicker') {
      if (!result.isState) {
        toastr('top-right', 'warning', result.message);
      }

      this.setState({
        sDateShow: false,
        eDateShow: false
      });
      // $('#end').click();
    }
  };

  isShow = (e, type) => {
    if (type === 'end') {
      this.setState({ eDateShow: false });
    } else {
      this.setState({ sDateShow: false });
    }
  };

  render() {
    const { style, loadData } = this.props,
      { month, sDateShow, eDateShow, startDate, endDate } = this.state,
      monthMinus = {
        t: moment().format('LL'),
        n0: moment().format('MM'),
        n1: moment()
          .subtract(1, 'month')
          .format('MM'),
        n2: moment()
          .subtract(2, 'month')
          .format('MM')
      };
    return (
      <Form.Field inline className="date-form" style={style}>
        <Input labelPosition="right" type="text" placeholder="Amount" className="form-element">
          <DatePicker
            id="start"
            className="startDate"
            selected={this.state.startDate}
            selectsStart
            startDate={this.state.startDate}
            endDate={this.state.endDate}
            onChange={(e) => this.handleChange(e, 'sDate', true)}
            onSelect={(e) => this.isShow(e, 'start')}
            dateFormat="YYYY-MM-DD"
            // maxDate={moment()}
            onFocus={() => this.calendarOpen('start')}
            onClickOutside={(e) => this.clickOutSide(e)}
            inline={sDateShow}
          />
          <Label basic>
            <div onClick={(e) => this.calendarOpen('start')}>
              <Icon disabled name="calendar" />
            </div>
          </Label>
        </Input>
        <Icon name="minus" className="datepicker-bar" />
        <Input labelPosition="right" type="text" placeholder="Amount" className="form-element">
          <DatePicker
            id="end"
            className="endDate"
            selected={this.state.endDate}
            selectsEnd
            startDate={this.state.startDate}
            endDate={this.state.endDate}
            onChange={(e) => this.handleChange(e, 'eDate', true)}
            onSelect={(e) => this.isShow(e, 'end')}
            dateFormat="YYYY-MM-DD"
            // maxDate={moment()}
            onFocus={() => this.calendarOpen('end')}
            onClickOutside={(e) => this.clickOutSide(e)}
            inline={eDateShow}
          />
          <Label basic>
            <div onClick={(e) => this.calendarOpen('end')}>
              <Icon disabled name="calendar" />
            </div>
          </Label>
        </Input>
      </Form.Field>
    );
  }
}

// DateRangeForm = connect(
//
//   state => ({
//
//     date : state.commons.date
//
//   }),
//   dispatch => ({
//
//     commonFn : bindActionCreators({
//       changeDate : cmAPI_Action.ReduxDataSet
//     }, dispatch)
//
//   })
// )(DateRangeForm)

export default DateRangeForm;
