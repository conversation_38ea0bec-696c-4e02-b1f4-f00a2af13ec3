import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import ReactTable from 'react-table';
import update from 'react-addons-update';
import scroll from 'helpers/scroll';
import styled from 'styled-components';
import { Button, Modal, Grid, Form, Popup } from 'semantic-ui-react';
import { Division, SearchInputBox, LoadingBar } from 'components/Commons';
import cm from 'helpers/commons';
import toastr from 'helpers/toastr';

import * as cm_action from 'actions/commons';

class CbUserModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      elements: [],
      headers: [
        { header: '', width: 56, isChecked: true, className: 'allCheckbox' },
        { header: 'ID', clickAction: true },
        { header: '이름' },
        { header: '직위' },
        { header: '직책' },
        { header: '사번' },
        { header: '상태' },
        { header: '사용자 타입' }
      ],
      heightIdx: 0,
      containerHeight: 620,
      isInfiniteLoading: true,
      allCheck: false,
      checkedStaff: []
    };
  }

  shouldComponentUpdate(nProps, nState) {
    let result = true;
    const { checkedStaff } = this.state;
    const { keyword } = this.props;
    const isCcheckedStaff = JSON.stringify(checkedStaff) !== JSON.stringify(nState.checkedStaff);
    const isCkeyword = JSON.stringify(keyword) !== JSON.stringify(nProps.keyword);
    // checkedStaff가 상태가 변경되도 render 하지 않는다.
    if (isCcheckedStaff && checkedStaff.length > 0) {
      result = false;
    }
    // 검색어 props가 변경되도 render 하지 않는다.
    if (isCkeyword) {
      result = false;
    }

    this.scrollHandler();
    return result;
  }

  componentWillReceiveProps(nextProps) {
    const { staffList: nStaffList } = nextProps;
    const { staffList } = this.props;
    const staffListChange = JSON.stringify(staffList) !== JSON.stringify(nStaffList);

    if (staffListChange) {
      this.scrollEvent(nextProps.staffList);
    }
  }

  scrollEvent = (staffList) => {
    if (staffList && staffList.data.user) {
      const { user } = staffList.data;
      this.setState({
        elements: this.buildElements(0, 100, user, false),
        isInfiniteLoading: false
      });
    } else {
      this.setState({ elements: [] });
    }
  };

  handleInfiniteLoad = async () => {
    const { staffList } = this.props;
    const { elements, heightIdx, containerHeight } = this.state;
    const item = staffList ? staffList.data.paging : null;

    let that = this;
    let elemLength = elements.length;
    let itemLength = item ? item.totalcount : 0;
    const infiniteParm = scroll.event(item, elements, containerHeight, heightIdx, elemLength);
    if (elemLength < itemLength) {
      this.setState({
        isInfiniteLoading: true,
        heightIdx: heightIdx + containerHeight
      });

      const { staffList } = this.props;
      let data = this.buildElements(elemLength, elemLength + 100, staffList.data.user, false);

      setTimeout(function() {
        that.setState({
          elements: that.state.elements.concat(data),
          heightIdx: infiniteParm.heightIdx,
          isInfiniteLoading: false
        });
      }, 500);
    } else {
      this.setState({
        // isInfiniteLoading : false
      });
    }
  };

  elementInfiniteLoad = () => {
    const { staffList } = this.props;
    let dom = <LoadingBar />;
    return dom;
  };

  buildElements = (start, end, item) => {
    try {
      let elements = [];
      if (item) {
        const user = item;
        for (let i = start; i < end; i++) {
          const temp = this.listItem(user[i]);
          if (temp) {
            elements.push(temp);
          }
        }
      }
      return elements;
    } catch (e) {
      console.log('###### e : ', e);
    } finally {
    }
  };

  listItem = (item) => {
    const { staffChecked } = this.props;
    const { headers } = this.state;
    let isDisabled = false;
    let result = null;
    if (!item) return;
    if (!item.group) {
      isDisabled = true;
    }
    isDisabled = item.policy.some((data) => {
      if (data.type === 'LONGTERM') {
        return true;
      }
    });

    if (item) {
      const division = cm.trTdDivision(item.orgCode);
      const userStatus = cm.statusTypeNameV2(item.status.code, item.dormant);
      const userGrade = cm.gradeTypeNameV2(item.grade.code);
      result = (
        <div className="rt-tr -odd" key={item.id} style={{ width: '100%' }}>
          <div className="rt-td" style={{ maxWidth: 56 }}>
            <div className="checkbox pure">
              <label>
                <input
                  type="checkbox"
                  id={item.id}
                  name={item.name}
                  className={`staff-checkbox ${item.signid}`}
                  value="on"
                  onClick={(e) => this.staffChecked(e, item)}
                />
                <span />
              </label>
            </div>
          </div>
          <div className="rt-td">{item.signid}</div>
          <div className="rt-td">{item.name}</div>
          {division.map((divi, i) => {
            return (
              <div
                key={`divi${i}`}
                className="rt-td"
                // style={{ flex: '128 0 auto', width: 150 }}
              >
                <Popup className="td-tooltip" inverted trigger={<span>{divi}</span>} content={divi} />
              </div>
            );
          })}
          <div className="rt-td">{item.position}</div>
          <div className="rt-td">{item.rankposition}</div>
          <div className="rt-td">{item.comidnum}</div>
          <div className="rt-td" style={{ color: userStatus.fontColor }}>
            {userStatus.text}
          </div>
          <div className="rt-td" style={{ color: userGrade.fontColor }}>
            {userGrade.text}
          </div>
        </div>
      );
      return result;
    } else {
      return null;
    }
  };

  tBodyFunc = (state) => {
    const { elements, containerHeight, isInfiniteLoading } = this.state;
    return (
      <div className="rt-tbody" style={state.style}>
        <div className="rt-tr-group">
          <Infinite
            className="infinite-scroll"
            elementHeight={43}
            containerHeight={containerHeight}
            infiniteLoadBeginEdgeOffset={containerHeight - 50}
            onInfiniteLoad={this.handleInfiniteLoad}
            loadingSpinnerDelegate={this.elementInfiniteLoad()}
            isInfiniteLoading={isInfiniteLoading}
            handleScroll={this.scrollHandler}
          >
            {elements}
          </Infinite>
        </div>
      </div>
    );
  };

  scrollHandler = (e) => {
    const { checkedStaff } = this.state;

    checkedStaff.forEach((item) => {
      const dom = $('#' + item.id);
      dom.prop('checked', true);
    });
  };

  noDataProps = (state) => {
    const { staffList } = this.props;
    let dom = <div className="rt-noData">검색 결과가 없습니다.</div>;
    if (staffList && staffList.data.user) {
      dom = <div className="rt-noData"></div>;
    }
    return dom;
  };

  //사용자 조회
  staffSel = async (params) => {
    this.setState({ isInfiniteLoading: true });
    const { cmAPI } = this.props;
    try {
      await cmAPI.staffSel(params);
      const { staffList } = this.props;
      if (staffList && staffList.data) {
        const { user } = staffList.data;
        const { checkedStaff } = this.state;
        const mapUserTemp = cm.excludeUseMap(checkedStaff);

        let allCheck = false;

        if (!user) return;

        user.forEach((item) => {
          if (!mapUserTemp.get(item.id)) {
            allCheck = true;
          }
        });

        if (allCheck) {
          $('.allCheckbox').prop('checked', false);
        } else {
          $('.allCheckbox').prop('checked', true);
        }
      }
    } catch (e) {
      const response = e.response;
      if (response) {
        toastr('top-right', 'error', response.data.message);
      }
      console.log('###### staffSel error : ', e);
    } finally {
      this.setState({ isInfiniteLoading: false });
    }
  };

  // 검색
  submitForm = async () => {
    const { keyword, depth } = this.props;
    const params = {
      keyword: keyword ? keyword.value : null,
      orgCode: depth ? depth.value : null
    };

    this.staffSel(params);
  };

  // ender 검색
  handleEntSubmit = (e) => {
    if (e.key === 'Enter') {
      this.submitForm();
    }
  };

  // 체크박스 클릭
  staffChecked = async (e, item) => {
    if (!item) return;

    const { checkedStaff } = this.state;
    let isChecked = e.target.checked;
    if (isChecked) {
      await this.setState({
        checkedStaff: update(this.state.checkedStaff, { $push: [item] })
      });
    } else {
      const idx = await cm.searchIdx(checkedStaff, 0, checkedStaff.length, item.id);
      await this.setState({
        checkedStaff: update(this.state.checkedStaff, { $splice: [[idx, 1]] })
      });
      $('.allCheckbox').prop('checked', false);
    }

    $('#' + item.id).prop('checked', isChecked);
  };

  // 체크박스 전체 클릭
  allCheck = async (e) => {
    const { checkedStaff } = this.state;
    const { staffList } = this.props;
    let isChecked = e.target.checked;

    if (isChecked) {
      if (staffList && staffList.data) {
        const { user } = staffList.data;
        const userTemp = user;
        const checkedTemp = checkedStaff;
        const allData = checkedTemp.concat(userTemp);
        const result = cm.removeDuplicates(allData, 'id');
        await this.setState({
          checkedStaff: update(this.state.checkedStaff, { $set: result })
        });
      }
    } else {
      const { user } = staffList.data;
      const userTemp = cm.removeDup(checkedStaff, user);
      await this.setState({
        checkedStaff: update(this.state.checkedStaff, { $set: userTemp })
      });
    }

    $('.staff-checkbox').prop('checked', isChecked);
  };
  // 팝업 open 시 action
  open = () => {
    this.setState({ checkedStaff: [] });
    this.staffSel();
  };
  // 팝업 close 시 action
  close = () => {
    const { closeFunc } = this.props.modal;
    closeFunc();
  };
  // 완료 버튼 클릭
  success = () => {
    const { checkedStaff } = this.state;
    const { modal } = this.props;
    modal.successFunc(checkedStaff);
    this.close();
  };

  render() {
    const { headers, checkedStaff } = this.state;
    const { modal, division, staffList, noDataText } = this.props;
    const fn = { fnChecked: this.allCheck, move: null };
    const header = cm.tableHeaderList(headers, 3, null, null, null, true);
    const columns = scroll.header(header, fn);
    let selUserCnt = 0;
    if (staffList && staffList.data && staffList.data.user) {
      const { user } = staffList.data;
      selUserCnt = user.length;
    }
    return (
      <Modal open={modal.open} onClose={this.close} size={modal.size} onMount={this.open}>
        <Modal.Header>사용자 추가</Modal.Header>
        <Modal.Content>
          <Grid reversed="tablet vertically">
            <Grid.Row className="">
              <Grid.Column>
                <Form>
                  <Form.Group>
                    <span
                      style={{
                        fontSize: 14,
                        fontWeight: 'bold',
                        lineHeight: 2,
                        marginRight: 10
                      }}
                    >
                      {`${checkedStaff.length} 명 / ${selUserCnt} 명`}
                    </span>
                    <SearchInputBox
                      handleSubmit={(e) => this.submitForm('text')}
                      handleEntSubmit={(e) => this.handleEntSubmit(e)}
                      placeHolder={'ID, 이름'}
                    />
                    <Division
                      division={division}
                      style={{ position: 'fixed', zIndex: 999 }}
                      onChangeFunc={(e) => this.submitForm('division')}
                    />
                  </Form.Group>
                </Form>
              </Grid.Column>
            </Grid.Row>
          </Grid>
          <ScReactTable
            className="react-table"
            noDataText={noDataText ? noDataText : '검색 결과가 없습니다.'}
            columns={columns}
            sortable={false}
            resizable={false}
            showPagination={false}
            TbodyComponent={this.tBodyFunc}
            NoDataComponent={this.noDataProps}
          />
        </Modal.Content>
        <Modal.Actions>
          <Button content="취소" basic onClick={this.close} />
          <Button positive content="완료" onClick={this.success} />
        </Modal.Actions>
      </Modal>
    );
  }
}

const ScReactTable = styled(ReactTable)`
  .rt-table {
    overflow-y: hidden;
  }
  .rt-tbody .rt-td {
    border-right: 0;
  }
  .rt-tbody .rt-td,
  .rt-thead .rt-td,
  .rt-thead .rt-th {
    padding-left: 16px !important;
    padding-top: 10px !important;
    padding-bottom: 8px !important;
  }
  .rt-tr .checkbox.pure input[type='checkbox']:checked + span:before {
    top: -5px;
  }
`;

CbUserModal = connect(
  (state) => ({
    staffList: state.commons.staff,
    division: state.commons.division,
    orgCode: state.commons.orgIdx,
    depth: state.commons.depth,
    keyword: state.commons.text
  }),
  (dispatch) => ({
    cmAPI: bindActionCreators(
      {
        staffSel: cm_action.StaffSel
      },
      dispatch
    )
  })
)(CbUserModal);

export default CbUserModal;
