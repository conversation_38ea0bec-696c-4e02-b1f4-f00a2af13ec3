import React, { ReactElement, useEffect, useState } from 'react';
import { Menu } from 'antd';
import { Route, Link, NavLink } from 'react-router-dom';

import { MenuWrapper } from './styles';
const { SubMenu } = Menu;

interface Props {
  data: [];
  location: any;
  theme?: 'dark' | 'light';
}

interface IMenu {
  children: [];
  title: string;
  to: string;
  idx: number | string;
}

export default function MenuView({ theme = 'dark', location, data = [] }: Props): ReactElement {
  return (
    <MenuWrapper
      theme={theme}
      mode="inline"
      openKeys={data && data.map(({ idx, to }, rIdx) => {
        return to || `menu-${idx}`;
      })}
      selectedKeys={[location.pathname]}
    >
      {data && data.map((menu: IMenu, rIdx) => {
        const { children, title, to, idx } = menu;

        if (children && children.length > 0) {
          return (
            <SubMenu key={to || `menu-${idx}`} title={title}>
              {children.map((m: IMenu, cIdx) => {
                return (
                  <Menu.Item key={m.to || `menu-${idx}-${cIdx}`}>
                    <NavLink
                      to={{
                        pathname: m.to,
                        state: { menuIdx: idx }
                      }}
                    >
                      {m.title}
                    </NavLink>
                  </Menu.Item>
                );
              })}
            </SubMenu>
          );
        }
        return (
          <Menu.Item key={to || `menu-${rIdx}`}>
            <NavLink
              to={{
                pathname: to,
                state: { menuIdx: idx }
              }}
            >
              {title}
            </NavLink>
          </Menu.Item>
        );
      })}
    </MenuWrapper>
  );
}
