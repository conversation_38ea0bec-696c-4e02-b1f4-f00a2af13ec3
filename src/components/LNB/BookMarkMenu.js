/*
  즐겨찾기 된 LNB 메뉴 리스트
*/
import React, { Component } from 'react';
import PropTypes from 'prop-types';

import { connect } from 'react-redux';
import menuList from 'config/menuList';
import { Icon } from 'semantic-ui-react';

const propTypes = {};

const defaultProps = {};

class BookMarkMenu extends Component {
  constructor(props) {
    super(props);
  }
  componentDidMount() {}
  render() {
    const { bookMarkDel, lnbMenuClick } = this.props.lnbMenuFn;
    const bookMark = this.props.menu.map((menu, idx) => {
      let url = this.context.router.history.location.pathname;
      let isActive = menu.url === url ? true : false;

      return (
        <div key={menu.code}>
          <li className={isActive ? 'menu-active' : ''} style={{ textAlign: 'left' }}>
            <div
              onClick={(e) => {
                lnbMenuClick(menu);
              }}
            >
              <a>{menu.name}</a>
            </div>
            <Icon
              name="star"
              size="small"
              color="green"
              onClick={() => {
                bookMarkDel(menu.idx);
              }}
            />
          </li>
        </div>
      );
    });
    return <ul className="bookMark-menu">{bookMark}</ul>;
  }
}

BookMarkMenu.contextTypes = {
  router: PropTypes.object.isRequired
};

BookMarkMenu.propTypes = propTypes;
BookMarkMenu.defaultProps = defaultProps;

export default BookMarkMenu;
