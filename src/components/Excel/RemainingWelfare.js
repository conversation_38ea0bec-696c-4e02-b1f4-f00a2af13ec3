import cm from 'helpers/commons';

const sheets = [
  {
    sheetName: 'data',
    fieldLabels: [
      'ID',
      '이름',
      '부서1',
      '부서2',
      '부서3',
      '부서4',
      '사원번호',
      '복지그룹',
      '복지정책',
      '잔여 복지포인트'
    ],
    fieldValues: (item) => {
      const division = cm.trTdDivision(item.orgCode, false) || [];
      const amount = item.policy ? item.policy.reduce((acc, cur) => acc + cur.amount, 0) : 0;

      return [
        item.signid,
        item.name,
        division[0] || '',
        division[1] || '',
        division[2] || '',
        division[3] || '',
        item.comidnum,
        item.group.name,
        item.policy ? item.policy[0].name : '',
        amount
      ];
    }
  }
];

export default sheets;
