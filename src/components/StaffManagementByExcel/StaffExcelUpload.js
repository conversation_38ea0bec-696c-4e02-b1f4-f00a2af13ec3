import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Field, reduxForm } from 'redux-form';
import { Icon, Button } from 'semantic-ui-react';

import { SikdaeGroupDtlModal } from 'components/StaffSignUp/Modal';
import { FileInput } from 'components/Commons';

import toastr from 'helpers/toastr';

import * as cm_action from 'actions/commons';
import * as com_action from 'actions/company';

class StaffExcelUpload extends Component {
  constructor(props) {
    super(props);

    this.state = {
      //그룹 정책 설명 모달
      open: false,
      //파일 업로드
      isLoading: false
    };
  }

  componentWillMount() {
    const { comAPI } = this.props;
    //식대그룹 리스트
    this.sikdaeGroupList();
  }

  //식대 그룹 조회
  sikdaeGroupList = async () => {
    const { cmAPI, sikdaeGroup } = this.props;

    try {
      const params = {
        viewType: 'all'
      };
      await cmAPI.sikdaeGroupList(params);
      const { sikdaeGroup } = this.props;

      //식대그룹이 있을 시 default 정책 조회
      if (sikdaeGroup) {
        //let idx = sikdaeGroup.data.group[0].idx;
        //let name = sikdaeGroup.data.group[0].name;
        let idx = 0;
        let name = null;

        //첫 화면 진입 시 식대정책 조회
        const { sikdaeGroupClick } = this.props.ExcelFn;
        sikdaeGroupClick(idx, name);
      }
    } catch (e) {
      console.log('###### sikdaeGroup : ', e);
    }
  };

  //open 식대 그룹 상세보기 Modal
  showModal = () => this.setState({ open: true });

  //close 식대 그룹 상세보기 Modal
  closeModal = () => this.setState({ open: false });

  //식대 그룹 정책
  sikdaePolicySel = (groupIdx, groupName) => {
    const { cmAPI } = this.props;

    try {
      this.showModal(groupName);
      cmAPI.sikdaePolicySel(groupIdx);
    } catch (e) {
      console.log('##### sikdaePolicySel  : ', e);
    }
  };

  formSubmit = (event, newValue, previousValue) => {
    const { method, groupNum, excelUpload } = this.props.ExcelFn;

    if (method === 'reg' && (!groupNum || groupNum <= 0)) {
      toastr('top-right', 'warning', '식대 그룹을 선택해주세요.');
    }

    let formData = new FormData();
    formData.append('file', newValue);
    formData.append('viewType', 'all');
    excelUpload(formData);
  };

  render() {
    const { activeStep, method, groupNum, groupName, sikdaeGroupClick } = this.props.ExcelFn,
      { sikdaeGroup } = this.props,
      varModal = {
        close: this.closeModal,
        open: this.state.open,
        name: groupName
      },
      uploadModal = {
        closeModal: this.closeUploadModal
      };

    let buttonList = sikdaeGroup
      ? sikdaeGroup.data.group.map((data, i) => {
          return (
            <Button
              key={data.idx}
              active={data.idx === groupNum ? true : false}
              onClick={(e) => {
                sikdaeGroupClick(data.idx, data.name);
              }}
            >
              <Icon
                name="info circle"
                size="large"
                color="grey"
                onClick={() => {
                  this.sikdaePolicySel(data.idx, data.name);
                }}
              />
              {data.name}
            </Button>
          );
        })
      : '';

    return (
      <div className="staff-upload">
        {method === 'reg' ? (
          <div>
            <div className="sub-title">식대그룹 선택하기</div>
            <div className="sik-group">
              {activeStep === 2 ? (
                <div>
                  <Button.Group basic>{buttonList}</Button.Group>
                  <div>
                    <div className="brief-text">
                      <p>* 추가할 사용자의 식대그룹을 선택해주세요.</p>
                      <p>* 한명은 하나의 식대그룹에만 소속될 수 있습니다.</p>
                      <p>
                        * 사용자는 식대그룹을 기준으로 등록되어 다른 식대그룹을 지정하고 싶다면, 엑셀파일을 분리하여
                        등록해주시길 바랍니다.
                      </p>
                    </div>
                    <SikdaeGroupDtlModal varModal={varModal} />
                  </div>
                </div>
              ) : (
                ''
              )}
              {activeStep === 3 ? (
                <Button.Group basic>
                  <Button active>{groupName}</Button>
                </Button.Group>
              ) : (
                ''
              )}
            </div>
          </div>
        ) : (
          ''
        )}

        {activeStep === 2 ? (
          <div>
            <div className="sub-title">사용자 엑셀파일 업로드하기</div>
            <div className="excel-upload">
              <Field name="file" component={FileInput} type="file" onChange={this.formSubmit} />

              <div className="brief-text">
                {method === 'del' ? (
                  <p>* 사용자 정보를 세가지 이상 입력하시면, 동명이인 혹은 퇴사처리 오류가 줄어듭니다.</p>
                ) : (
                  ''
                )}
                <p>* 새로운 파일 업로드 시 기존정보는 삭제됩니다.</p>
              </div>
            </div>
          </div>
        ) : (
          ''
        )}
      </div>
    );
  }
}

StaffExcelUpload = connect(
  (state) => ({
    //commons
    sikdaeGroup: state.commons.sikdaeGroup
  }),
  (dispatch) => ({
    cmAPI: bindActionCreators(
      {
        sikdaeGroupList: cm_action.SikdaeGroupList,
        sikdaePolicySel: cm_action.SikdaePolicyList
      },
      dispatch
    )
  })
)(StaffExcelUpload);

export default reduxForm({
  form: 'fileupload'
})(StaffExcelUpload);
