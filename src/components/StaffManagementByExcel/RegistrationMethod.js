import toastr from 'helpers/toastr';
import moment from 'moment-timezone';
import React, { useEffect, useState } from 'react';
import { Button, Checkbox, Form } from 'semantic-ui-react';

import cm from 'helpers/commons';
import storage from 'helpers/storage';
import { find, maxBy, sortBy } from 'lodash';
import * as commons from 'services/commons';
import { getMeInfo } from 'apis/captainpayment';

import { DeleteExcelLayout, RegistrationExcelLayout, UpdateExcelLayout } from 'excelLayout';

const METHOD_NAME = { reg: '추가', upd: '수정', del: '퇴사' };
const EXCEL_LABEL = { reg: '신규 사용자 등록', upd: '기존 사용자 정보 수정', del: '사용자 퇴사처리' };
const EXCEL_LAYOUT = { reg: RegistrationExcelLayout, upd: UpdateExcelLayout, del: DeleteExcelLayout };

function RegistrationMethod({ RegMethodFn }) {
  const com = storage.get('company');
  const { activeStep, method, methodSel } = RegMethodFn;

  const [isLoading, setIsLoading] = useState(false);
  const [captainPaymentGrade, setCaptainPaymentGrade] = useState('');
  const [employeeAuth, setEmployeeAuth] = useState(cm.authGroupSet('employee'));

  const { serviceList } = storage.get('companyInfo') || {};

  useEffect(() => {
    getMeInfo().then((data) => {
      setCaptainPaymentGrade(data?.user?.captainPaymentGrade || '');
    });
  }, []);

  const sheetLayoutJson = EXCEL_LAYOUT[method];
  const isActiveSikdae = find(serviceList, { serviceType: 'SIKDAE' })?.status === 'ACTIVE';
  const isActiveWelfare = find(serviceList, { serviceType: 'WELFARE' })?.status === 'ACTIVE';
  const isRegisterLayout = method === 'reg';
  const isUpdateLayout = method === 'upd';

  useEffect(() => {
    if (isLoading) {
      toastr('top-right', 'warning', '엑셀파일을 생성 중입니다.');
    }
  }, [isLoading]);

  const fetchEmployeeData = async (size) => {
    let page = 1;
    let allUsers = [];
    let keepFetching = true;

    // const apiStartTime = Date.now();

    while (keepFetching) {
      const { data } = await commons.GetEmployeeList({ page, size });
      const responseUsers = data?.users || [];
      allUsers = allUsers.concat(responseUsers);

      if (responseUsers.length < size) {
        keepFetching = false;
      } else {
        page += 1;
      }
    }

    // const apiEndTime = Date.now();
    // console.log(`API time: ${(apiEndTime - apiStartTime) / 1000} s`);

    const result = sortBy(allUsers, ['name', 'signId']); // 모든 데이터를 반환
    // console.log(`Sorting time: ${(Date.now() - apiEndTime) / 1000} s`);

    return result;
  };

  const downloadExcelForm = async () => {
    setIsLoading(true);

    const sheetData = {}; // Info: Sheet 지정 params
    const currentDateTime = moment();
    const fileName = `임직원관리_${METHOD_NAME[method]}_엑셀서식_${currentDateTime.format('YYYYMMDD_kkmmss')}`;

    sheetData['0'] = {
      customValues: [
        { address: 'B2', value: EXCEL_LABEL[method] },
        { address: 'C3', value: `${fileName}.xlsx` },
        { address: 'C4', value: window.location.origin },
        { address: 'C5', value: currentDateTime.format('YYYY-MM-DD kk:mm:ss') }
      ]
    };

    try {
      if (isRegisterLayout || isUpdateLayout) {
        const currentDivisions = {}; // Info: 부서 관련하여 동일한 format 으로 정리하기 위한 변수
        const targetExcelDatas = []; // Info: API 에서 받아온 데이터 지정 변수
        const newCols = []; // Info: 새롭게 추가할 col 관련 변수 (현재는 부서 관련한 col)
        const removeColumns = []; // Info: 고객사가 이용하는 Service 에 따라 필요없는 col 을 제거하기 위한 변수
        const colValidations = {}; // Info: 특정 Cell 에 대한 Validation 을 담기 위한 변수 (layout 을 통해 추출한 값을 복사 & 붙여넣기)
        const tableInfos = []; // Info: Table 로 값 삽입하기 위한 변수

        const allDivisions = storage.get('navi');
        const divisionDepth = maxBy(allDivisions, 'depth').depth;

        allDivisions.forEach((division) => {
          const names = division.name.split('>');
          while (names.length < divisionDepth) {
            names.push(null);
          }
          currentDivisions[division.orgCode] = names;
        });

        if (isUpdateLayout) {
          const allUserInfos = await fetchEmployeeData(1000);
          if (allUserInfos.length < 1) throw new Error('API 호출 중 문제가 발생했습니다.');

          allUserInfos?.forEach((userInfo, index) => {
            const {
              id,
              signId,
              name,
              orgCode,
              status,
              email,
              cellPhone,
              rankPosition,
              position,
              comidnum,
              mealGroup,
              welfareGroup
            } = userInfo;

            const userDivisions = currentDivisions[orgCode] || new Array(divisionDepth);

            targetExcelDatas.push([
              index + 1,
              id || '',
              signId || '',
              name || '',
              ...userDivisions,
              status || '',
              email || '',
              cellPhone || '',
              rankPosition || '',
              position || '',
              comidnum || '',
              mealGroup?.groupName || '',
              mealGroup?.groupIdx || '',
              welfareGroup?.groupName || '',
              welfareGroup?.groupIdx || ''
            ]);
          });
        } else {
          // Info: Data 를 입력하지 않으면 정상적으로 validation 이 걸리지 않음. 그래서 빈 값이라도 넣어줘야 함
          new Array(10000).fill().forEach((e, index) => {
            targetExcelDatas.push([index + 1]);
          });
        }

        const { data: allGroups } = await commons.GroupsExcelData();

        if (isActiveSikdae) {
          const sikdaeGroupData = allGroups.mealGroups.map(({ name, idx }) => [name, idx]);
          const sikdaeGroupName = allGroups.mealGroups.map(({ name }) => name);

          tableInfos.push({
            name: 'sikdaeGroupTable',
            ref: 'A1',
            style: { theme: 'TableStyleLight1' },
            columns: [{ name: '식대그룹명' }, { name: '식대그룹 코드' }],
            rows: sikdaeGroupData
          });

          colValidations['12'] = {
            minRowNumber: isRegisterLayout ? '11' : '13',
            dataValidation: {
              type: 'list',
              formulae: ['"' + sikdaeGroupName.join() + '"'], // Info: 배열이지만... excelJS 라이브러리에서 배열에 하나의 String 으로 넣어줘야 정상 동작함
              allowBlank: true,
              showInputMessage: true,
              showErrorMessage: true,
              errorTitle: 'Error',
              error: '올바른 값을 선택해주세요'
            }
          };
        }

        if (isActiveWelfare) {
          const welfareGroupData = allGroups.welfareGroups.map(({ name, idx }) => [name, idx]);
          const welfareGroupName = allGroups.welfareGroups.map(({ name }) => name);

          tableInfos.push({
            name: 'welfareGroupTable',
            ref: isActiveSikdae ? 'D1' : 'A1',
            style: { theme: 'TableStyleLight1' },
            columns: [{ name: '복지그룹명' }, { name: '복지그룹 코드' }],
            rows: welfareGroupData
          });

          colValidations[isActiveSikdae ? '14' : '12'] = {
            minRowNumber: isRegisterLayout ? '11' : '13',
            dataValidation: {
              type: 'list',
              formulae: ['"' + welfareGroupName.join() + '"'],
              allowBlank: true,
              showInputMessage: true,
              showErrorMessage: true,
              errorTitle: 'Error',
              error: '올바른 값을 선택해주세요'
            }
          };
        }

        for (let i = 0; i < divisionDepth - 1; i++) {
          newCols.push({ rowNum: isRegisterLayout ? 10 : 12, value: `부서${i + 2}` });
        }

        const customFormulas = [
          {
            startAddress: isRegisterLayout ? 'M11' : 'M13', // Info: 수식 시작 위치
            refAddress: isRegisterLayout ? 'L11' : 'L13', // Info: 수식에서 참조할 셀 위치
            formula: (refAddress) => `IFERROR(VLOOKUP(${refAddress}, '그룹 정보'!A$2:B$100, 2, FALSE), "")`
          },
          {
            startAddress: isRegisterLayout ? 'O11' : 'O13',
            refAddress: isRegisterLayout ? 'N11' : 'N13',
            formula: (refAddress) => `IFERROR(VLOOKUP(${refAddress}, '그룹 정보'!D$2:E$100, 2, FALSE), "")`
          }
        ].slice(0, tableInfos.length); // Info: TableInfos 의 갯수만큼 수식이 필요. (1개 or 2개);

        !isActiveSikdae && removeColumns.push({ startColNum: 12, removeColCount: 2 });
        !isActiveWelfare && removeColumns.push({ startColNum: 14, removeColCount: 2 });

        sheetData['1'] = {
          insertColNum: isRegisterLayout ? 5 : 6, // Info: 부서 삽입 colNum 인데 등록 파일과 수정 파일의 위치가 다름
          newCols,
          startRowNum: isRegisterLayout ? 11 : 13,
          newRows: targetExcelDatas,
          customFormulas,
          removeColumns, // Info: 이용하는 서비스(식권대장, 복지대장)가 1개 이상 없을 때 관련 col 축소
          colValidations
        };

        sheetData['2'] = { tableInfos };
      }

      handleFileExport(sheetLayoutJson, sheetData, fileName);
      toastr('top-right', 'success', '엑셀파일 생성이 완료 되었습니다.');
    } catch (error) {
      toastr('top-right', 'error', '엑셀파일 생성에 실패 하였습니다.');
    } finally {
      setIsLoading(false);
    }
  };

  let btnTitle = '';
  let disabledReg = false;
  let disabledUpd = false;
  let disabledDel = false;
  let showReg = true;
  let showUpd = true;
  let showDel = true;

  if (activeStep === 1) {
    if (method === 'reg') {
      btnTitle = '신규 등록 ';
    } else if (method === 'upd') {
      btnTitle = '사용자 목록 ';
    } else if (method === 'del') {
      btnTitle = '퇴사처리 ';
    }
  } else if (activeStep === 2) {
    if (method === 'reg') {
      disabledUpd = true;
      disabledDel = true;
    } else if (method === 'upd') {
      disabledReg = true;
      disabledDel = true;
    } else if (method === 'del') {
      disabledReg = true;
      disabledUpd = true;
    }
  } else if (activeStep === 3) {
    if (method === 'reg') {
      showUpd = false;
      showDel = false;
    } else if (method === 'upd') {
      showReg = false;
      showDel = false;
    } else if (method === 'del') {
      showReg = false;
      showUpd = false;
    }
  }

  return (
    <div className="reg-method">
      {activeStep <= 3 ? (
        <div>
          <div className="sub-title">등록 방식 선택하기</div>
          <Form.Field className="reg-sel">
            {employeeAuth.all || employeeAuth.create ? (
              showReg ? (
                <Checkbox
                  radio
                  label="신규 사용자 추가"
                  value="reg"
                  disabled={disabledReg}
                  checked={method === 'reg'}
                  onChange={methodSel}
                />
              ) : (
                ''
              )
            ) : null}
            {employeeAuth.all || employeeAuth.update ? (
              showUpd ? (
                <Checkbox
                  radio
                  label="기존 사용자 정보수정"
                  value="upd"
                  disabled={disabledUpd}
                  checked={method === 'upd'}
                  onChange={methodSel}
                />
              ) : (
                ''
              )
            ) : null}
            {employeeAuth.all || employeeAuth.delete ? (
              showDel ? (
                <Checkbox
                  radio
                  label="사용자 퇴사처리"
                  value="del"
                  disabled={disabledDel}
                  checked={method === 'del'}
                  onChange={methodSel}
                />
              ) : (
                ''
              )
            ) : null}
          </Form.Field>
        </div>
      ) : null}
      {activeStep === 3 && showDel ? (
        <div className="brief-text">
          <p>
            * 퇴사처리 후 식대 <span className="txt-red">지급 및 사용이 즉시 중지 </span>됩니다.
          </p>
          <p>
            * My포인트 환불에 대해선 <span className="txt-red">식권대장에 문의</span>해주셔야 합니다.
          </p>
          <p>
            * 계정 탈퇴 후 <span className="txt-red">복구가 불가능</span>하오니, 신중히 처리해주시길 바랍니다.
          </p>
        </div>
      ) : (
        ''
      )}

      {activeStep === 1 ? (
        <div className="download-excel">
          <Button inverted color="green" loading={isLoading} onClick={downloadExcelForm}>
            {btnTitle}엑셀서식 다운로드
          </Button>

          {method === 'reg' ? (
            <div className="brief-text">
              <p>* 엑셀서식을 다운로드하여 신규 입사자 정보를 입력해주세요.</p>
              <p>
                * 사용자는 식대그룹을 기준으로 등록되어 다른 식대그룹을 지정하고 싶다면, 엑셀파일을 분리하여
                등록해주시길 바랍니다.
              </p>
              <p>* 신규 사용자는 일반사용자 권한으로 등록됩니다.</p>
              <p>* 신규 사용자의 초기 비밀번호는 숫자 0000으로 설정됩니다.</p>
            </div>
          ) : (
            ''
          )}
          {method === 'upd' ? (
            <div className="brief-text">
              <p>* 사용자목록 엑셀서식을 다운로드 후 사용자 정보를 변경해주세요.</p>
              <p>* 부서관리자의 부서변경 시 관리자 권한을 해제 후 변경해주세요.</p>
              <p>
                * 기존 사용자의 <b>부서, 이메일, 휴대전화번호, 직위, 직책, 사원번호 정보, 회원상태</b>를 변경할 수
                있습니다.
              </p>
              <p>
                * <b>활성상태</b>는 활성, 일시정지, 일시정지예약으로 구성됩니다.
              </p>
              <p>
                * <b>일시정지예약</b>은 엑셀 G열 내에 YYYY-MM-DD HH:00의 형식으로 입력해야합니다. (예 : 2023-12-31
                00:00)
              </p>
              <p>
                * 목록에 추가된 부서로만 부서 이동이 가능합니다. 신규 부서로 이동을 원하시면, 사용자 관리에서 부서 추가
                후 엑셀파일을 작성해주세요.
              </p>
              <p>* 식대 그룹 변경 시 기존 식대 포인트는 모두 초기화됩니다.</p>
            </div>
          ) : (
            ''
          )}
          {method === 'del' ? (
            <div className={method === 'del' ? 'brief-text active' : 'brief-text'}>
              <p>* 엑셀서식을 다운로드 후 퇴사처리할 사용자의 정보를 입력해주세요.</p>
              <p>* 사용자 정보를 세가지 이상 입력하시면, 동명이인 혹은 퇴사처리 오류가 줄어듭니다.</p>
            </div>
          ) : (
            ''
          )}
        </div>
      ) : (
        ''
      )}
    </div>
  );
}

export default RegistrationMethod;
