{"0": {"workSheet": {"conditionalFormattings": [], "headerFooter": {"oddFooter": "&C&\"Helvetica Neue,Regular\"&12&K000000&P"}, "id": 1, "name": "메타정보", "pageSetup": {"fitToPage": false, "margins": {"left": 0.7, "right": 0.7, "top": 0.75, "bottom": 0.75, "header": 0.5, "footer": 0.5}, "orientation": "portrait", "horizontalDpi": 4294967295, "verticalDpi": 4294967295, "pageOrder": "downThenOver", "blackAndWhite": false, "draft": false, "cellComments": "None", "errors": "displayed", "scale": 100, "fitToWidth": 1, "fitToHeight": 1, "firstPageNumber": 1, "useFirstPageNumber": true, "usePrinterDefaults": false, "copies": 1}, "properties": {"defaultRowHeight": 16, "dyDescent": 0, "outlineLevelRow": 0, "outlineLevelCol": 0, "defaultColWidth": 8.83203125}, "rowBreaks": [], "state": "visible", "tables": {}, "views": [{"workbookViewId": 0, "rightToLeft": false, "state": "normal", "showRuler": true, "showRowColHeaders": true, "showGridLines": false, "zoomScale": 100, "zoomScaleNormal": 100, "activeCell": "B8"}]}, "colValidations": {}, "rowLayouts": [{"number": 1, "style": {}, "height": 89}, {"number": 2, "style": {}, "height": 45}, {"number": 3, "style": {}, "height": 24}, {"number": 4, "style": {}, "height": 24}, {"number": 5, "style": {}, "height": 24}, {"number": 6, "style": {}, "height": 24}, {"number": 7, "style": {}, "height": 200}, {"number": 8, "style": {}, "height": 18}], "colLayouts": [{"width": 6.33203125, "collapsed": false, "defn": {"width": 6.33203125, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "A", "number": 1, "outlineLevel": 0}, {"width": 27.6640625, "collapsed": false, "defn": {"width": 27.6640625, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "B", "number": 2, "outlineLevel": 0}, {"width": 78.83203125, "collapsed": false, "defn": {"width": 78.83203125, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "C", "number": 3, "outlineLevel": 0}, {"width": 5.83203125, "collapsed": false, "defn": {"width": 5.83203125, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "D", "number": 4, "outlineLevel": 0}], "cellDatas": [{"address": "A1", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin", "color": {"indexed": 10}}, "top": {"style": "thin", "color": {"indexed": 10}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}}, "value": null, "col": 1, "row": 1}, {"address": "B1", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 10}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}}, "value": null, "col": 2, "row": 1}, {"address": "C1", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 10}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}}, "value": null, "col": 3, "row": 1}, {"address": "D1", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 10}}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 4, "row": 1}, {"address": "A2", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 10}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left"}}, "value": null, "col": 1, "row": 2}, {"address": "B2", "style": {"numFmt": "@", "font": {"bold": true, "size": 16, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left"}}, "value": "신규 사용자 등록", "col": 2, "row": 2}, {"address": "C2", "style": {"font": {"bold": true, "size": 16, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left"}}, "value": null, "col": 3, "row": 2}, {"address": "D2", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}}, "value": null, "col": 4, "row": 2}, {"address": "A3", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 10}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left"}}, "value": null, "col": 1, "row": 3}, {"address": "B3", "style": {"numFmt": "@", "font": {"bold": true, "size": 12, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left"}}, "value": "파일명", "col": 2, "row": 3}, {"address": "C3", "style": {"font": {"size": 12, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left"}}, "value": null, "col": 3, "row": 3}, {"address": "D3", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "left"}}, "value": null, "col": 4, "row": 3}, {"address": "A4", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 10}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left"}}, "value": null, "col": 1, "row": 4}, {"address": "B4", "style": {"numFmt": "@", "font": {"bold": true, "size": 12, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left"}}, "value": "출처", "col": 2, "row": 4}, {"address": "C4", "style": {"font": {"size": 12, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left"}}, "value": null, "col": 3, "row": 4}, {"address": "D4", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "left"}}, "value": null, "col": 4, "row": 4}, {"address": "A5", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 10}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left"}}, "value": null, "col": 1, "row": 5}, {"address": "B5", "style": {"numFmt": "@", "font": {"bold": true, "size": 12, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left"}}, "value": "다운로드 날짜", "col": 2, "row": 5}, {"address": "C5", "style": {"numFmt": "@", "font": {"size": 12, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left"}}, "value": null, "col": 3, "row": 5}, {"address": "D5", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "left"}}, "value": null, "col": 4, "row": 5}, {"address": "A6", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 10}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}}, "value": null, "col": 1, "row": 6}, {"address": "B6", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left"}}, "value": null, "col": 2, "row": 6}, {"address": "C6", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left"}}, "value": null, "col": 3, "row": 6}, {"address": "D6", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 4, "row": 6}, {"address": "A7", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 10}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}}, "value": null, "col": 1, "row": 7}, {"address": "B7", "style": {"numFmt": "@", "font": {"bold": true, "size": 12, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "개인정보보호", "col": 2, "row": 7}, {"address": "C7", "style": {"numFmt": "@", "font": {"size": 12, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 3, "charset": 129}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle", "wrapText": true}}, "value": {"richText": [{"font": {"size": 12, "color": {"argb": "FFFF0000"}, "name": "맑은 고딕", "family": 3, "charset": 129}, "text": "※ 본 정보는 식권대장 서비스 외 사용을 엄격히 금지합니다.  \n※ 계약 시 작성한 개인정보보호정책을 준수합니다.  "}, {"font": {"size": 12, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "text": "\nㅡ\n대표전화 : 1644-5047\n이메일 : <EMAIL>\n서울특별시 강남구 봉은사로103길 5, 4층\nㅡ\n식권대장 개인정보보호담당자 : 한선호CTO  \n<EMAIL> | 1644-5047  "}]}, "col": 3, "row": 7}, {"address": "D7", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 4, "row": 7}, {"address": "A8", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 10}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}}, "value": null, "col": 1, "row": 8}, {"address": "B8", "style": {"font": {"bold": true, "size": 12, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 2, "row": 8}, {"address": "C8", "style": {"numFmt": "@", "font": {"size": 12, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left"}}, "value": null, "col": 3, "row": 8}, {"address": "D8", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 4, "row": 8}], "images": [{"range": {"editAs": "oneCell", "br": {"nativeCol": 1, "nativeColOff": 927100, "nativeRow": 0, "nativeRowOff": 812800}, "tl": {"nativeCol": 1, "nativeColOff": 101600, "nativeRow": 0, "nativeRowOff": 304800}}, "imageUrl": "data:image/png;base64,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"}]}, "1": {"workSheet": {"conditionalFormattings": [], "headerFooter": {"oddFooter": "&C&\"Helvetica Neue,Regular\"&12&K000000&P"}, "id": 2, "name": "신규 사용자 등록", "pageSetup": {"fitToPage": false, "margins": {"left": 0.7, "right": 0.7, "top": 0.75, "bottom": 0.75, "header": 0.5, "footer": 0.5}, "orientation": "portrait", "horizontalDpi": 4294967295, "verticalDpi": 4294967295, "pageOrder": "downThenOver", "blackAndWhite": false, "draft": false, "cellComments": "None", "errors": "displayed", "scale": 100, "fitToWidth": 1, "fitToHeight": 1, "firstPageNumber": 1, "useFirstPageNumber": true, "usePrinterDefaults": false, "copies": 1}, "properties": {"defaultRowHeight": 16, "dyDescent": 0, "outlineLevelRow": 0, "outlineLevelCol": 0, "defaultColWidth": 8.83203125}, "rowBreaks": [], "state": "visible", "tables": {}, "views": [{"workbookViewId": 0, "rightToLeft": false, "state": "normal", "showRuler": true, "showRowColHeaders": true, "showGridLines": false, "zoomScale": 100, "zoomScaleNormal": 100, "activeCell": "J27"}]}, "colValidations": {"12": {"minRowNumber": 11, "dataValidation": {"type": "list", "formulae": ["\"부장,차장,팀장,사원,무제한만\""], "allowBlank": true, "showInputMessage": true, "showErrorMessage": true, "errorTitle": "Error", "error": "올바른 값을 선택해주세요"}}, "14": {"minRowNumber": 11, "dataValidation": {"type": "list", "formulae": ["\"몰라회사 복지그룹\""], "allowBlank": true, "showInputMessage": true, "showErrorMessage": true, "errorTitle": "Error", "error": "올바른 값을 선택해주세요"}}}, "rowLayouts": [{"number": 1, "style": {}, "height": 44}, {"number": 2, "style": {}, "height": 28}, {"number": 3, "style": {}, "height": 19}, {"number": 4, "style": {}, "height": 19}, {"number": 5, "style": {}, "height": 19}, {"number": 6, "style": {}, "height": 19}, {"number": 7, "style": {}, "height": 19}, {"number": 8, "style": {}, "height": 19}, {"number": 9, "style": {}, "height": 24}, {"number": 10, "style": {}, "height": 21}, {"number": 11, "style": {}, "height": 17}, {"number": 12, "style": {}, "height": 17}, {"number": 13, "style": {}, "height": 17}, {"number": 14, "style": {}, "height": 17}, {"number": 15, "style": {}, "height": 17}, {"number": 16, "style": {}, "height": 17}, {"number": 17, "style": {}, "height": 17}, {"number": 18, "style": {}, "height": 17}, {"number": 19, "style": {}, "height": 17}, {"number": 20, "style": {}, "height": 17}], "colLayouts": [{"width": 9.1640625, "collapsed": false, "defn": {"width": 9.1640625, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "A", "number": 1, "outlineLevel": 0}, {"width": 18, "collapsed": false, "defn": {"width": 18, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "B", "number": 2, "outlineLevel": 0}, {"width": 18, "collapsed": false, "defn": {"width": 18, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "C", "number": 3, "outlineLevel": 0}, {"width": 18, "collapsed": false, "defn": {"width": 18, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "D", "number": 4, "outlineLevel": 0}, {"width": 22.1640625, "collapsed": false, "defn": {"width": 22.1640625, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "E", "number": 5, "outlineLevel": 0}, {"width": 22.1640625, "collapsed": false, "defn": {"width": 22.1640625, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "F", "number": 6, "outlineLevel": 0}, {"width": 22.1640625, "collapsed": false, "defn": {"width": 22.1640625, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "G", "number": 7, "outlineLevel": 0}, {"width": 22.1640625, "collapsed": false, "defn": {"width": 22.1640625, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "H", "number": 8, "outlineLevel": 0}, {"width": 22.1640625, "collapsed": false, "defn": {"width": 22.1640625, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "I", "number": 9, "outlineLevel": 0}, {"width": 22.1640625, "collapsed": false, "defn": {"width": 22.1640625, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "J", "number": 10, "outlineLevel": 0}, {"width": 22.1640625, "collapsed": false, "defn": {"width": 22.1640625, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "K", "number": 11, "outlineLevel": 0}, {"width": 22.1640625, "collapsed": false, "defn": {"width": 22.1640625, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "L", "number": 12, "outlineLevel": 0}, {"width": 22.1640625, "collapsed": false, "defn": {"width": 22.1640625, "style": {}, "hidden": true, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": true, "isCustomWidth": true, "isDefault": false, "letter": "M", "number": 13, "outlineLevel": 0}, {"width": 22.1640625, "collapsed": false, "defn": {"width": 22.1640625, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "N", "number": 14, "outlineLevel": 0}, {"width": 22.1640625, "collapsed": false, "defn": {"width": 22.1640625, "style": {}, "hidden": true, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": true, "isCustomWidth": true, "isDefault": false, "letter": "O", "number": 15, "outlineLevel": 0}], "cellDatas": [{"address": "A1", "style": {"numFmt": "@", "font": {"bold": true, "size": 20, "color": {"indexed": 12}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 10}}, "top": {"style": "thin", "color": {"indexed": 10}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "center", "vertical": "middle"}}, "value": null, "col": 1, "row": 1}, {"address": "B1", "style": {"numFmt": "@", "font": {"bold": true, "size": 20, "color": {"indexed": 12}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"top": {"style": "thin", "color": {"indexed": 13}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "신규 사용자 등록", "col": 2, "row": 1}, {"address": "C1", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 13}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 3, "row": 1}, {"address": "D1", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 13}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 4, "row": 1}, {"address": "E1", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 10}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 5, "row": 1}, {"address": "F1", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 10}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 6, "row": 1}, {"address": "G1", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 10}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 7, "row": 1}, {"address": "H1", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 10}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 8, "row": 1}, {"address": "I1", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 10}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 9, "row": 1}, {"address": "J1", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 10}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 10, "row": 1}, {"address": "K1", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 10}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 11, "row": 1}, {"address": "L1", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 10}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 12, "row": 1}, {"address": "M1", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 10}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 13, "row": 1}, {"address": "N1", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"right": {"style": "thin", "color": {"indexed": 64}}, "top": {"style": "thin", "color": {"indexed": 10}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 14, "row": 1}, {"address": "O1", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 10}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 15, "row": 1}, {"address": "A2", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin", "color": {"indexed": 10}}, "top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "center", "vertical": "middle"}}, "value": null, "col": 1, "row": 2}, {"address": "B2", "style": {"numFmt": "@", "font": {"bold": true, "size": 12, "color": {"indexed": 8}, "name": "Malgun Gothic", "family": 2, "charset": 129}, "border": {"top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "주의사항", "col": 2, "row": 2}, {"address": "C2", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 3, "row": 2}, {"address": "D2", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 4, "row": 2}, {"address": "E2", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 5, "row": 2}, {"address": "F2", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 6, "row": 2}, {"address": "G2", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 7, "row": 2}, {"address": "H2", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 8, "row": 2}, {"address": "I2", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 9, "row": 2}, {"address": "J2", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 10, "row": 2}, {"address": "K2", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 11, "row": 2}, {"address": "L2", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 12, "row": 2}, {"address": "M2", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 13, "row": 2}, {"address": "N2", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"right": {"style": "thin", "color": {"indexed": 64}}, "top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 14, "row": 2}, {"address": "O2", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 15, "row": 2}, {"address": "A3", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin", "color": {"indexed": 10}}, "top": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "center"}}, "value": null, "col": 1, "row": 3}, {"address": "B3", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": {"richText": [{"font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "text": "▶ ID, 이름, 부서1, 식대그룹, 복지그룹"}, {"font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "text": "은 필수 입력입니다."}]}, "col": 2, "row": 3}, {"address": "C3", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 3, "row": 3}, {"address": "D3", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 4, "row": 3}, {"address": "E3", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 5, "row": 3}, {"address": "F3", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 6, "row": 3}, {"address": "G3", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 7, "row": 3}, {"address": "H3", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 8, "row": 3}, {"address": "I3", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 9, "row": 3}, {"address": "J3", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 10, "row": 3}, {"address": "K3", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 11, "row": 3}, {"address": "L3", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 12, "row": 3}, {"address": "M3", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 13, "row": 3}, {"address": "N3", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"right": {"style": "thin", "color": {"indexed": 64}}, "top": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 14, "row": 3}, {"address": "O3", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"top": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 15, "row": 3}, {"address": "A4", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin", "color": {"indexed": 10}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "center"}}, "value": null, "col": 1, "row": 4}, {"address": "B4", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "▶ 임직원 관리에 등록된 부서와 동일한 텍스트를 입력해주시면, 엑셀파일로 임직원 부서 이동을 처리할 수 있습니다.", "col": 2, "row": 4}, {"address": "C4", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 3, "row": 4}, {"address": "D4", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 4, "row": 4}, {"address": "E4", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 5, "row": 4}, {"address": "F4", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 6, "row": 4}, {"address": "G4", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 7, "row": 4}, {"address": "H4", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 8, "row": 4}, {"address": "I4", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 9, "row": 4}, {"address": "J4", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 10, "row": 4}, {"address": "K4", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 11, "row": 4}, {"address": "L4", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 12, "row": 4}, {"address": "M4", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 13, "row": 4}, {"address": "N4", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"right": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 14, "row": 4}, {"address": "O4", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 15, "row": 4}, {"address": "A5", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin", "color": {"indexed": 10}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "center"}}, "value": null, "col": 1, "row": 5}, {"address": "B5", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "▶ 신규 부서 생성 및 이동을 원하시면 임직원 관리에서 부서를 생성한 후 동일한 부서명을 입력해주세요.  ", "col": 2, "row": 5}, {"address": "C5", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 3, "row": 5}, {"address": "D5", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 4, "row": 5}, {"address": "E5", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 5, "row": 5}, {"address": "F5", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 6, "row": 5}, {"address": "G5", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 7, "row": 5}, {"address": "H5", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 8, "row": 5}, {"address": "I5", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 9, "row": 5}, {"address": "J5", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 10, "row": 5}, {"address": "K5", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 11, "row": 5}, {"address": "L5", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 12, "row": 5}, {"address": "M5", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 13, "row": 5}, {"address": "N5", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"right": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 14, "row": 5}, {"address": "O5", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 15, "row": 5}, {"address": "A6", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin", "color": {"indexed": 10}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "center"}}, "value": null, "col": 1, "row": 6}, {"address": "B6", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "▶ 셀서식은 변경할 수 없습니다. 하늘색 셀은 필수 입력입니다.", "col": 2, "row": 6}, {"address": "C6", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 3, "row": 6}, {"address": "D6", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 4, "row": 6}, {"address": "E6", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 5, "row": 6}, {"address": "F6", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 6, "row": 6}, {"address": "G6", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 7, "row": 6}, {"address": "H6", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 8, "row": 6}, {"address": "I6", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 9, "row": 6}, {"address": "J6", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 10, "row": 6}, {"address": "K6", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 11, "row": 6}, {"address": "L6", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 12, "row": 6}, {"address": "M6", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 13, "row": 6}, {"address": "N6", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"right": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 14, "row": 6}, {"address": "O6", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 15, "row": 6}, {"address": "A7", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin", "color": {"indexed": 10}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "center"}}, "value": null, "col": 1, "row": 7}, {"address": "B7", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "▶ 입력한 정보는 전부 '텍스트' 형태로 변경하여 업로드 부탁드립니다. ( 전체 선택 > 홈 > 표시형식> 드롭 다운 선택 (목록 선택) > 텍스트 )", "col": 2, "row": 7}, {"address": "C7", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 3, "row": 7}, {"address": "D7", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 4, "row": 7}, {"address": "E7", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 5, "row": 7}, {"address": "F7", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 6, "row": 7}, {"address": "G7", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 7, "row": 7}, {"address": "H7", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 8, "row": 7}, {"address": "I7", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 9, "row": 7}, {"address": "J7", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 10, "row": 7}, {"address": "K7", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 11, "row": 7}, {"address": "L7", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 12, "row": 7}, {"address": "M7", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 13, "row": 7}, {"address": "N7", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"right": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 14, "row": 7}, {"address": "O7", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 15, "row": 7}, {"address": "A8", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin", "color": {"indexed": 10}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "center"}}, "value": null, "col": 1, "row": 8}, {"address": "B8", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 2, "row": 8}, {"address": "C8", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 3, "row": 8}, {"address": "D8", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 4, "row": 8}, {"address": "E8", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 5, "row": 8}, {"address": "F8", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 6, "row": 8}, {"address": "G8", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 7, "row": 8}, {"address": "H8", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 8, "row": 8}, {"address": "I8", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 9, "row": 8}, {"address": "J8", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 10, "row": 8}, {"address": "K8", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 11, "row": 8}, {"address": "L8", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 12, "row": 8}, {"address": "M8", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 13, "row": 8}, {"address": "N8", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"right": {"style": "thin", "color": {"indexed": 64}}, "bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 14, "row": 8}, {"address": "O8", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"bottom": {"style": "thin", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": null, "col": 15, "row": 8}, {"address": "A9", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "center"}}, "value": "예시", "col": 1, "row": 9}, {"address": "B9", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "sikdae", "col": 2, "row": 9}, {"address": "C9", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "김식대", "col": 3, "row": 9}, {"address": "D9", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "영업부", "col": 4, "row": 9}, {"address": "E9", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"vertical": "middle"}}, "value": "<EMAIL>", "col": 5, "row": 9}, {"address": "F9", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "01012345678", "col": 6, "row": 9}, {"address": "G9", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "대리", "col": 7, "row": 9}, {"address": "H9", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "팀장", "col": 8, "row": 9}, {"address": "I9", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "*********", "col": 9, "row": 9}, {"address": "J9", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "남", "col": 10, "row": 9}, {"address": "K9", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "19770707", "col": 11, "row": 9}, {"address": "L9", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left"}}, "value": "", "col": 12, "row": 9}, {"address": "M9", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}}, "value": "", "col": 13, "row": 9}, {"address": "N9", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left"}}, "value": "", "col": 14, "row": 9}, {"address": "O9", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "thin", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left"}}, "value": "", "col": 15, "row": 9}, {"address": "A10", "style": {"numFmt": "@", "font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "center", "vertical": "middle"}}, "value": "#", "col": 1, "row": 10}, {"address": "B10", "style": {"numFmt": "@", "font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": {"richText": [{"font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "text": " ID "}, {"font": {"bold": true, "size": 10, "color": {"indexed": 11}, "name": "맑은 고딕", "family": 2, "charset": 129}, "text": "(필수입력)"}]}, "note": {"texts": [{"font": {"size": 11, "color": {"indexed": 8}, "name": "Helvetica Neue", "family": 2}, "text": "Microsoft Office 사용자:\nID : 필수입력\n\n> 6~40자\n> 영문, 숫자, 특수문자 : -(하이픈), _(언더라인), @(골뱅이), .(마침표)\n> 대소문자 구분 없음\n"}], "margins": {"insetmode": "auto", "inset": [0.13, 0.25, 0.25]}, "anchor": "\n    2, 6, 8, 14, 4, 2, 12, 16", "editAs": "absolute", "protection": {}}, "col": 2, "row": 10}, {"address": "C10", "style": {"numFmt": "@", "font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": {"richText": [{"font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "text": "이름 "}, {"font": {"bold": true, "size": 10, "color": {"indexed": 11}, "name": "맑은 고딕", "family": 2, "charset": 129}, "text": "(필수입력)"}]}, "note": {"texts": [{"font": {"size": 11, "color": {"indexed": 8}, "name": "Helvetica Neue", "family": 2}, "text": "Microsoft Office 사용자:\n이름 : 필수입력\n"}], "margins": {"insetmode": "auto", "inset": [0.13, 0.25, 0.25]}, "anchor": "\n    3, 6, 8, 14, 4, 110, 12, 16", "editAs": "absolute", "protection": {}}, "col": 3, "row": 10}, {"address": "D10", "style": {"numFmt": "@", "font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": {"richText": [{"font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "text": "부서1 "}, {"font": {"bold": true, "size": 10, "color": {"indexed": 11}, "name": "맑은 고딕", "family": 2, "charset": 129}, "text": "(필수입력)"}]}, "note": {"texts": [{"font": {"size": 11, "color": {"indexed": 8}, "name": "Helvetica Neue", "family": 2}, "text": "Microsoft Office 사용자:\n부서1 : 필수입력\n\n> 임직원 관리에서 [부서1]에 등록된 부서명과 동일한 텍스트를 입력하시면 해당 부서 소속으로 등록됩니다.\n"}], "margins": {"insetmode": "auto", "inset": [0.13, 0.25, 0.25]}, "anchor": "\n    3, 14, 8, 14, 5, 35, 12, 16", "editAs": "absolute", "protection": {}}, "col": 4, "row": 10}, {"address": "E10", "style": {"numFmt": "@", "font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "이메일 (중복불가)", "note": {"texts": [{"font": {"size": 11, "color": {"indexed": 8}, "name": "Helvetica Neue", "family": 2}, "text": "Microsoft Office 사용자:\n이메일 : 중복불가, 선택입력\n\n> 이메일은 50자까지 입력할 수 있습니다.\n> 이메일은 아이디/비밀번호 찾기를 위해 입력해주시길 바랍니다."}], "margins": {"insetmode": "auto", "inset": [0.13, 0.25, 0.25]}, "anchor": "\n    4, 14, 8, 14, 6, 60, 12, 16", "editAs": "absolute", "protection": {}}, "col": 5, "row": 10}, {"address": "F10", "style": {"numFmt": "@", "font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "휴대전화번호", "note": {"texts": [{"font": {"size": 11, "color": {"indexed": 8}, "name": "Helvetica Neue", "family": 2}, "text": "Microsoft Office 사용자:\n휴대전화 번호 : 선택입력\n\n> 입력 형식 : 01012345678, 010-1234-5678, 010 1234 5678\n> 휴대전화번호는 아이디/비밀번호 찾기를 위해 입력해주시길 바랍니다."}], "margins": {"insetmode": "auto", "inset": [0.13, 0.25, 0.25]}, "anchor": "\n    5, 14, 8, 14, 8, 85, 12, 16", "editAs": "absolute", "protection": {}}, "col": 6, "row": 10}, {"address": "G10", "style": {"numFmt": "@", "font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "직위", "note": {"texts": [{"font": {"size": 11, "color": {"indexed": 8}, "name": "Helvetica Neue", "family": 2}, "text": "Microsoft Office 사용자:\n직위 : 선택입력\n\n> 10자 까지 입력할수 있습니다.\n"}], "margins": {"insetmode": "auto", "inset": [0.13, 0.25, 0.25]}, "anchor": "\n    6, 14, 8, 14, 10, 85, 12, 16", "editAs": "absolute", "protection": {}}, "col": 7, "row": 10}, {"address": "H10", "style": {"numFmt": "@", "font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "직책", "note": {"texts": [{"font": {"size": 11, "color": {"indexed": 8}, "name": "Helvetica Neue", "family": 2}, "text": "Microsoft Office 사용자:\n직책 : 선택입력\n\n> 10자 까지 입력할수 있습니다."}], "margins": {"insetmode": "auto", "inset": [0.13, 0.25, 0.25]}, "anchor": "\n    8, 14, 8, 14, 12, 0, 12, 16", "editAs": "absolute", "protection": {}}, "col": 8, "row": 10}, {"address": "I10", "style": {"numFmt": "@", "font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "사원번호", "note": {"texts": [{"font": {"size": 11, "color": {"indexed": 8}, "name": "Helvetica Neue", "family": 2}, "text": "Microsoft Office 사용자:\n사원번호 : 선택입력\n\n> 15자 까지 입력할수 있습니다."}], "margins": {"insetmode": "auto", "inset": [0.13, 0.25, 0.25]}, "anchor": "\n    10, 14, 8, 14, 13, 11, 12, 16", "editAs": "absolute", "protection": {}}, "col": 9, "row": 10}, {"address": "J10", "style": {"numFmt": "@", "font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "성별", "note": {"texts": [{"font": {"size": 11, "color": {"indexed": 8}, "name": "Helvetica Neue", "family": 2}, "text": "Microsoft Office 사용자:\n성별 : 선택입력\n\n> 남/여/구분안함\n> 동명인 구분을 위해 입력받습니다.\n\n"}], "margins": {"insetmode": "auto", "inset": [0.13, 0.25, 0.25]}, "anchor": "\n    13, 0, 8, 14, 14, 0, 12, 16", "editAs": "absolute", "protection": {}}, "col": 10, "row": 10}, {"address": "K10", "style": {"numFmt": "@", "font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "생년월일", "note": {"texts": [{"font": {"size": 11, "color": {"argb": "FF000000"}, "name": "Helvetica Neue", "family": 2}, "text": "Microsoft Office "}, {"font": {"size": 11, "color": {"argb": "FF000000"}, "name": "Helvetica Neue", "family": 2}, "text": "사용자"}, {"font": {"size": 11, "color": {"argb": "FF000000"}, "name": "Helvetica Neue", "family": 2}, "text": ":\n"}, {"font": {"size": 11, "color": {"argb": "FF000000"}, "name": "Helvetica Neue", "family": 2}, "text": "생년월일"}, {"font": {"size": 11, "color": {"argb": "FF000000"}, "name": "Helvetica Neue", "family": 2}, "text": " : "}, {"font": {"size": 11, "color": {"argb": "FF000000"}, "name": "Helvetica Neue", "family": 2}, "text": "선택입력"}, {"font": {"size": 11, "color": {"argb": "FF000000"}, "name": "Helvetica Neue", "family": 2}, "text": "\n"}, {"font": {"size": 11, "color": {"argb": "FF000000"}, "name": "Helvetica Neue", "family": 2}, "text": "\n"}, {"font": {"size": 11, "color": {"argb": "FF000000"}, "name": "Helvetica Neue", "family": 2}, "text": "> 2017-7-7 (YYYY-MM-DD)\n"}, {"font": {"size": 11, "color": {"argb": "FF000000"}, "name": "Helvetica Neue", "family": 2}, "text": "> "}, {"font": {"size": 11, "color": {"argb": "FF000000"}, "name": "Helvetica Neue", "family": 2}, "text": "동명인"}, {"font": {"size": 11, "color": {"argb": "FF000000"}, "name": "Helvetica Neue", "family": 2}, "text": " "}, {"font": {"size": 11, "color": {"argb": "FF000000"}, "name": "Helvetica Neue", "family": 2}, "text": "구분을"}, {"font": {"size": 11, "color": {"argb": "FF000000"}, "name": "Helvetica Neue", "family": 2}, "text": " "}, {"font": {"size": 11, "color": {"argb": "FF000000"}, "name": "Helvetica Neue", "family": 2}, "text": "위해"}, {"font": {"size": 11, "color": {"argb": "FF000000"}, "name": "Helvetica Neue", "family": 2}, "text": " "}, {"font": {"size": 11, "color": {"argb": "FF000000"}, "name": "Helvetica Neue", "family": 2}, "text": "입력받습니다"}, {"font": {"size": 11, "color": {"argb": "FF000000"}, "name": "Helvetica Neue", "family": 2}, "text": "."}], "margins": {"insetmode": "auto", "inset": [0.13, 0.25, 0.25]}, "anchor": "\n    15, 15, 8, 14, 16, 11, 12, 16", "editAs": "absolute", "protection": {}}, "col": 11, "row": 10}, {"address": "L10", "style": {"numFmt": "@", "font": {"bold": true, "size": 10, "color": {"indexed": 11}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "식대그룹(필수입력)", "col": 12, "row": 10}, {"address": "M10", "style": {"numFmt": "@", "font": {"bold": true, "size": 10, "color": {"argb": "FFFF0000"}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"vertical": "middle"}}, "value": "식대코드(자동입력)", "col": 13, "row": 10}, {"address": "N10", "style": {"numFmt": "@", "font": {"bold": true, "size": 10, "color": {"indexed": 11}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "복지그룹(필수입력)", "col": 14, "row": 10}, {"address": "O10", "style": {"numFmt": "@", "font": {"bold": true, "size": 10, "color": {"indexed": 11}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"left": {"style": "thin", "color": {"indexed": 8}}, "right": {"style": "thin", "color": {"indexed": 8}}, "top": {"style": "medium", "color": {"indexed": 8}}, "bottom": {"style": "medium", "color": {"indexed": 8}}}, "fill": {"type": "pattern", "pattern": "solid", "fgColor": {"indexed": 9}}, "alignment": {"horizontal": "left", "vertical": "middle"}}, "value": "복지코드(자동입력)", "col": 15, "row": 10}, {"address": "A11", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "center", "vertical": "middle"}, "protection": {"locked": false, "hidden": false}}, "value": 1, "col": 1, "row": 11}, {"address": "B11", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 2, "row": 11}, {"address": "C11", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 3, "row": 11}, {"address": "D11", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 4, "row": 11}, {"address": "E11", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 5, "row": 11}, {"address": "F11", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 6, "row": 11}, {"address": "G11", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 7, "row": 11}, {"address": "H11", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 8, "row": 11}, {"address": "I11", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 9, "row": 11}, {"address": "J11", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 10, "row": 11}, {"address": "K11", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 11, "row": 11}, {"address": "L11", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 12, "row": 11}, {"address": "M11", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(L11, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 13, "row": 11}, {"address": "N11", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 14, "row": 11}, {"address": "O11", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(N11, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 15, "row": 11}, {"address": "A12", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "center", "vertical": "middle"}, "protection": {"locked": false, "hidden": false}}, "value": 2, "col": 1, "row": 12}, {"address": "B12", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 2, "row": 12}, {"address": "C12", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 3, "row": 12}, {"address": "D12", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 4, "row": 12}, {"address": "E12", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 5, "row": 12}, {"address": "F12", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 6, "row": 12}, {"address": "G12", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 7, "row": 12}, {"address": "H12", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 8, "row": 12}, {"address": "I12", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 9, "row": 12}, {"address": "J12", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 10, "row": 12}, {"address": "K12", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 11, "row": 12}, {"address": "L12", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 12, "row": 12}, {"address": "M12", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(L12, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 13, "row": 12}, {"address": "N12", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 14, "row": 12}, {"address": "O12", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(N12, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 15, "row": 12}, {"address": "A13", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "center", "vertical": "middle"}, "protection": {"locked": false, "hidden": false}}, "value": 3, "col": 1, "row": 13}, {"address": "B13", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 2, "row": 13}, {"address": "C13", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 3, "row": 13}, {"address": "D13", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 4, "row": 13}, {"address": "E13", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 5, "row": 13}, {"address": "F13", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 6, "row": 13}, {"address": "G13", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 7, "row": 13}, {"address": "H13", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 8, "row": 13}, {"address": "I13", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 9, "row": 13}, {"address": "J13", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 10, "row": 13}, {"address": "K13", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 11, "row": 13}, {"address": "L13", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 12, "row": 13}, {"address": "M13", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(L13, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 13, "row": 13}, {"address": "N13", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 14, "row": 13}, {"address": "O13", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(N13, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 15, "row": 13}, {"address": "A14", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "center", "vertical": "middle"}, "protection": {"locked": false, "hidden": false}}, "value": 4, "col": 1, "row": 14}, {"address": "B14", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 2, "row": 14}, {"address": "C14", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 3, "row": 14}, {"address": "D14", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 4, "row": 14}, {"address": "E14", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 5, "row": 14}, {"address": "F14", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 6, "row": 14}, {"address": "G14", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 7, "row": 14}, {"address": "H14", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 8, "row": 14}, {"address": "I14", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 9, "row": 14}, {"address": "J14", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 10, "row": 14}, {"address": "K14", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 11, "row": 14}, {"address": "L14", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 12, "row": 14}, {"address": "M14", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(L14, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 13, "row": 14}, {"address": "N14", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 14, "row": 14}, {"address": "O14", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(N14, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 15, "row": 14}, {"address": "A15", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "center", "vertical": "middle"}, "protection": {"locked": false, "hidden": false}}, "value": 5, "col": 1, "row": 15}, {"address": "B15", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 2, "row": 15}, {"address": "C15", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 3, "row": 15}, {"address": "D15", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 4, "row": 15}, {"address": "E15", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 5, "row": 15}, {"address": "F15", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 6, "row": 15}, {"address": "G15", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 7, "row": 15}, {"address": "H15", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 8, "row": 15}, {"address": "I15", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 9, "row": 15}, {"address": "J15", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 10, "row": 15}, {"address": "K15", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 11, "row": 15}, {"address": "L15", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 12, "row": 15}, {"address": "M15", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(L15, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 13, "row": 15}, {"address": "N15", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 14, "row": 15}, {"address": "O15", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(N15, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 15, "row": 15}, {"address": "A16", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "center", "vertical": "middle"}, "protection": {"locked": false, "hidden": false}}, "value": 6, "col": 1, "row": 16}, {"address": "B16", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 2, "row": 16}, {"address": "C16", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 3, "row": 16}, {"address": "D16", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 4, "row": 16}, {"address": "E16", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 5, "row": 16}, {"address": "F16", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 6, "row": 16}, {"address": "G16", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 7, "row": 16}, {"address": "H16", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 8, "row": 16}, {"address": "I16", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 9, "row": 16}, {"address": "J16", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 10, "row": 16}, {"address": "K16", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 11, "row": 16}, {"address": "L16", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 12, "row": 16}, {"address": "M16", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(L16, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 13, "row": 16}, {"address": "N16", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 14, "row": 16}, {"address": "O16", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(N16, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 15, "row": 16}, {"address": "A17", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "center", "vertical": "middle"}, "protection": {"locked": false, "hidden": false}}, "value": 7, "col": 1, "row": 17}, {"address": "B17", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 2, "row": 17}, {"address": "C17", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 3, "row": 17}, {"address": "D17", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 4, "row": 17}, {"address": "E17", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 5, "row": 17}, {"address": "F17", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 6, "row": 17}, {"address": "G17", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 7, "row": 17}, {"address": "H17", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 8, "row": 17}, {"address": "I17", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 9, "row": 17}, {"address": "J17", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 10, "row": 17}, {"address": "K17", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 11, "row": 17}, {"address": "L17", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 12, "row": 17}, {"address": "M17", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(L17, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 13, "row": 17}, {"address": "N17", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 14, "row": 17}, {"address": "O17", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(N17, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 15, "row": 17}, {"address": "A18", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "center", "vertical": "middle"}, "protection": {"locked": false, "hidden": false}}, "value": 8, "col": 1, "row": 18}, {"address": "B18", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 2, "row": 18}, {"address": "C18", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 3, "row": 18}, {"address": "D18", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 4, "row": 18}, {"address": "E18", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 5, "row": 18}, {"address": "F18", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 6, "row": 18}, {"address": "G18", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 7, "row": 18}, {"address": "H18", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 8, "row": 18}, {"address": "I18", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 9, "row": 18}, {"address": "J18", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 10, "row": 18}, {"address": "K18", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 11, "row": 18}, {"address": "L18", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 12, "row": 18}, {"address": "M18", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(L18, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 13, "row": 18}, {"address": "N18", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 14, "row": 18}, {"address": "O18", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(N18, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 15, "row": 18}, {"address": "A19", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "center", "vertical": "middle"}, "protection": {"locked": false, "hidden": false}}, "value": 9, "col": 1, "row": 19}, {"address": "B19", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 2, "row": 19}, {"address": "C19", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 3, "row": 19}, {"address": "D19", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 4, "row": 19}, {"address": "E19", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 5, "row": 19}, {"address": "F19", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 6, "row": 19}, {"address": "G19", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 7, "row": 19}, {"address": "H19", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 8, "row": 19}, {"address": "I19", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 9, "row": 19}, {"address": "J19", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 10, "row": 19}, {"address": "K19", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 11, "row": 19}, {"address": "L19", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 12, "row": 19}, {"address": "M19", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(L19, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 13, "row": 19}, {"address": "N19", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 14, "row": 19}, {"address": "O19", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(N19, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 15, "row": 19}, {"address": "A20", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "center", "vertical": "middle"}, "protection": {"locked": false, "hidden": false}}, "value": 10, "col": 1, "row": 20}, {"address": "B20", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 2, "row": 20}, {"address": "C20", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 3, "row": 20}, {"address": "D20", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 4, "row": 20}, {"address": "E20", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 5, "row": 20}, {"address": "F20", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 6, "row": 20}, {"address": "G20", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 7, "row": 20}, {"address": "H20", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 8, "row": 20}, {"address": "I20", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 9, "row": 20}, {"address": "J20", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 10, "row": 20}, {"address": "K20", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 11, "row": 20}, {"address": "L20", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 12, "row": 20}, {"address": "M20", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(L20, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 13, "row": 20}, {"address": "N20", "style": {"numFmt": "@", "font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}, "protection": {"locked": false, "hidden": false}}, "value": null, "col": 14, "row": 20}, {"address": "O20", "style": {"font": {"size": 10, "color": {"indexed": 8}, "name": "<PERSON><PERSON>", "family": 2}, "border": {"left": {"style": "thin"}, "right": {"style": "thin"}, "top": {"style": "thin"}, "bottom": {"style": "thin"}}, "fill": {"type": "pattern", "pattern": "none"}}, "value": {"formula": "IFERROR(VLOOKUP(N20, '그룹 정보'!#REF!, 2, FALSE), \"\")"}, "col": 15, "row": 20}], "images": []}, "2": {"workSheet": {"conditionalFormattings": [], "headerFooter": null, "id": 3, "name": "그룹 정보", "pageSetup": {"fitToPage": false, "margins": {"left": 0.7, "right": 0.7, "top": 0.75, "bottom": 0.75, "header": 0.3, "footer": 0.3}, "orientation": "portrait", "horizontalDpi": 4294967295, "verticalDpi": 4294967295, "pageOrder": "downThenOver", "blackAndWhite": false, "draft": false, "cellComments": "None", "errors": "displayed", "scale": 100, "fitToWidth": 1, "fitToHeight": 1, "firstPageNumber": 1, "useFirstPageNumber": false, "usePrinterDefaults": false, "copies": 1}, "properties": {"defaultRowHeight": 16, "dyDescent": 0, "outlineLevelRow": 0, "outlineLevelCol": 0, "defaultColWidth": 8.83203125}, "rowBreaks": [], "state": "visible", "tables": {}, "views": [{"workbookViewId": 0, "rightToLeft": false, "state": "normal", "showRuler": true, "showRowColHeaders": true, "showGridLines": true, "zoomScale": 100, "zoomScaleNormal": 100, "activeCell": "G38"}]}, "colValidations": {}, "rowLayouts": [{"number": 1, "style": {}, "height": 16}, {"number": 2, "style": {}, "height": 16}, {"number": 3, "style": {}, "height": 16}, {"number": 4, "style": {}, "height": 16}, {"number": 5, "style": {}, "height": 16}, {"number": 6, "style": {}, "height": 16}], "colLayouts": [], "cellDatas": [], "images": []}, "workbook": {"created": "2017-10-12T04:44:38.000Z", "creator": "", "lastModifiedBy": "", "modified": "2024-07-25T06:43:02.000Z", "views": [{"x": 5800, "y": -21600, "width": 19200, "height": 21600, "visibility": "visible", "activeTab": 1}], "properties": {"date1904": false}}}