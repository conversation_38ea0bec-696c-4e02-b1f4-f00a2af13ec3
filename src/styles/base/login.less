@import (multiple) '../company.config';

.login {
  height: 100%;
  background: @loginBackgroundColor;
  margin: 0 !important;
}

.login .login-form .login-form-col {
  .rem-px(height, 648);
  .rem-px(width, 480) !important;
  padding: 0 !important;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); //#0000025d;
}

.login .login-form .login-form-col.form-recommend {
  .rem-px(height, 719);
  .rem-px(width, 480) !important;
  padding: 0 !important;
  background: #ffffff;
}

.login .login-form .login-form-col .header {
  .rem-px(margin-top, 75);
  .rem-px(margin-right, 45);
  .rem-px(margin-left, 45);
  .rem-px(margin-bottom, 40);
  float: left;
}

.login .login-form .login-form-col .header.center {
  float: none;
}

.login .login-form .login-form-col .header img {
  .rem-px(width, 180);
  font-style: normal;
  font-weight: normal;
  font-size: 15px;
  line-height: 22px;
  letter-spacing: -0.7px;
  color: #6d7582;
  // .rem-px(height, 88);
}

.login .login-form .login-form-col .header > p {
  // .rem-px(margin-top, @marginDefault16);
  // .rem-px(font-size, 24);
  text-align: center;
  color: @textColor1;
  font-weight: normal;
  margin-top: 8px;
  font-style: normal;
  font-size: 15px;
  line-height: 22px;
  color: #8a8c8d;
  letter-spacing: -0.5px;
}

.login .login-form .login-form-col .header > div {
  .rem-px(margin-top, @marginDefault16);
  .rem-px(font-size, 18);
}

.login .login-form .login-form-col .header > div > .pwd-recommend {
  margin: 0;
  text-align: center;
  color: @textColor1;
  font-weight: normal;
}

.login .login-form .login-form-col .form-input:first-child {
  .rem-px(margin-top, 28);
}

.login .login-form .login-form-col .form-input:nth-child(2) {
  .rem-px(margin-bottom, 10);
}

.login .login-form .login-form-col .form-input label {
  .rem-px(font-size, 15px);
  font-style: normal;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: -1px;
  color: #333333;
  margin-bottom: 10px;
}

.login .login-form .login-form-col .form-input .input {
  width: 100%;
}

.login .login-form .login-form-col .form-input .input input {
  .rem-px(border-radius, 4);
  .rem-px(height, 48);
  .rem-px(font-size, @vendysH5);
  font-weight: bold;
}

.login .login-form .login-form-col .form-input .input input:focus {
  border-color: @loginInputFocusColor;
}

.login .login-form .login-form-col .field {
  .rem-px(margin-bottom, @marginDefault16);
}

.login .login-form .login-form-col form {
  .foo(0, 88);
  margin: 0px 45px 0px 45px;
  text-align: left;
}

.login .login-form .login-form-col .form-group {
  position: relative;
  margin: 0;
  justify-content: space-between;
  align-content: center;
  align-items: center;
  margin-bottom: 52px;
  margin-top: 16px;
}

.login .login-form .login-form-col .form-group button {
  display: flex;
  align-items: center;
  text-align: center;
  letter-spacing: -1px;
  text-decoration-line: underline;
  color: #8a8c8d;
  font-style: normal;
  font-weight: normal;
  font-size: 13px;
  padding: 0;
  line-height: 24px;
  text-shadow: none;
  background-color: white;
  border: none;
  box-shadow: none;
}

.login .login-form .login-form-col form > button {
  .rem-px(height, 54);
  .rem-px(margin-bottom, 20);
  .rem-px(font-size, 16);
  .rem-px(border-radius, 4);
  line-height: 24px;
  font-weight: normal;
  padding-top: 10px;
}

.login .login-form .login-form-col form > .button.skip {
  .rem-px(margin-bottom, 10);
  border: solid #cccccc;
  .rem-px(border-width, 1);
  background-color: #ffffff;
}

.login .login-form .login-form-col form > .password-desc {
  text-align: left;
  .rem-px(font-size, 16);
  .rem-px(margin-bottom, 16);
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #999999;
}

.login .login-form .login-form-col form > .password-desc > p {
  margin: 0;
}

.login .login-form .login-form-col form > p {
  text-align: center;
  .rem-px(font-size, 16);
  .rem-px(line-height, 24);
  font-style: normal;
  font-weight: normal;
  text-align: center;
  letter-spacing: -1px;
  color: #333333;
  opacity: 0.5;
  margin: 0;
}

.login-test .login-form-test .login-form-col-test form > button {
  .rem-px(width, 304);
  .rem-px(height, 48);
  .rem-px(margin-bottom, 24);
  .rem-px(font-size, 22);
  line-height: 0.5;
  font-weight: normal;
}

.login .login-form .login-form-col .login-form-footer {
  width: 100%;
  height: 60px;
  position: absolute;
  background: #f3f5f6;
  border-radius: 0 0 8px 8px;
  bottom: 0;
}

.login .login-form .login-form-col .login-form-footer .login-form-footer-img {
  position: absolute;
  left: 150px;
  top: 17px;
}

.login .login-form .login-form-col .form-checkbox {
  padding: 0;
  margin: 0;
}

.login .login-form .login-form-col .form-checkbox label:before {
  width: 24px;
  height: 24px;
}

.login .login-form .login-form-col .form-checkbox label:after {
  width: 24px;
  height: 24px;
  margin-top: 2px;
}

.login .login-form .login-form-col .form-checkbox label {
  font-style: normal;
  font-weight: normal;
  font-size: 13px;
  line-height: 19px;
  letter-spacing: -1px;
  color: #8a8c8d;
  padding-left: 30px;
  padding-top: 4px;
  height: 24px;
}

.login .login-form .login-form-col .login-form-footer img {
  width: 180px;
}
