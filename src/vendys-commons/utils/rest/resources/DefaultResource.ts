import React, { ReactElement } from 'react';
import { v4 as uuidv4 } from 'uuid';

export default class Resource {
    id:string = uuidv4();
    // _fetch = undefined
    host:string = "";
    url:string = "";
    method:string = "GET";

    bindingProps:string[] = [];
    manipulator = (p:any, d:any, isNotFirst:boolean)=>d;
    results?:(object|[]) = undefined;
    requestInterval?:number = undefined; // 5000 일경우
    _timeout?:number = undefined;
    _restParams?:object = {};

    constructor(id:string){
        if(id){
            this.id = id;
        }
    }

    init(){
        if(this._timeout){
            clearTimeout(this._timeout);
            this._timeout = undefined;
        }
    }

    set fetch(fn){
        this._fetch = fn;
    }

    // _fetch(props:any):any{
    //     return new Promise((resolve, reject)=>{
    //         return resolve();
    //     });
    // }

    __fetch(props:object, contextFn:object):any{
        if(!this._fetch) return;
        var p = this._fetch(props, contextFn);
        // props 에 값이 없어서 조건을 충족하지 못햇음.
        if(!p) return;

        if(p.constructor == Promise){
            return p
            .then(result=>{
                this.results = this.manipulator(props, result);
                return this.results;
            })
            .finally(()=>{
            })
        }else{
            return p;
        }

    }

    set updateFetch(fn){
        this._updateFetch = fn;
    }

    // updateFetch(props:object):object{
    //     return this._updateFetch(props);
    // }

    __updateFetch(props:object):object{
        if(this._updateFetch) {
            return this._updateFetch(props)
            .then(result=>{
                this.results = this.manipulator(props, result, true);
                return this.results;
            })
            .finally(()=>{
            })
        }
        return this._fetch(props);
    }

    rests:any[] = [
        // object 일경우에는 만들어주기,
        // function 일경우에는 props을 넣어주기,
        // Promise 일경우
    ];

    setManipulator(fn:()=>{}){
        this.manipulator = fn;
    }

    // manipulator
    _mainpulator(props:object, data:object, isNotFirst:boolean): object{
        return this.manipulator(props, data, isNotFirst);
    }

    _componentDidUnmount(){
    }

}
