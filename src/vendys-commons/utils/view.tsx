import React from 'react';
import { Modal as AntdModal, notification, Button } from 'antd';
import { FormattedMessage } from 'react-intl'
import {ExclamationCircleOutlined, DeleteOutlined, FileAddOutlined, EditOutlined, CloseOutlined } from '@ant-design/icons';
import {getIntl} from 'vendys/translations/LanguageProvider';

export const ConditionalWrapper = ({ condition, wrapper, children }) => 
  condition ? wrapper(children) : children;

const ModalTypes = {
  delete: {
    title: 'delete',
    content: "delete.confirm",
    okText: 'delete',
    cancelText: 'cancel',
    icon: <DeleteOutlined />
  },
  cancelQuick: {
    title: 'captain.quick',
    content: "reservation.cancel.confirm",
    okText: 'reservation.cancel',
    cancelText: 'cancel',
    icon: <CloseOutlined />
  },
  update: {
    title: 'update',
    content: "update.confirm",
    okText: 'update',
    cancelText: 'cancel',
    icon: <EditOutlined />
  },
  add: {
    title: 'add',
    content: "add.confirm",
    okText: 'add',
    cancelText: 'cancel',
    icon: <FileAddOutlined />
  },
}

export const ModalT = ({type, onOk = ()=>{}, onCancel = ()=>{}})=>{
  const {title, icon, content, okText, cancelText, onlyConfirm = false} = ModalTypes[type];
  const intl= getIntl();

  const m = AntdModal.confirm({
    title: intl.formatMessage({id: title, defaultMessage: ''}),
    icon: icon || <ExclamationCircleOutlined />,
    content: intl.formatMessage({id: content,defaultMessage: ''}),
    okText: intl.formatMessage({id: okText,defaultMessage: ''}),
    cancelText: intl.formatMessage({id: cancelText,defaultMessage: ''}),
    cancelButtonProps: { disabled: onlyConfirm },
    
    onOk: async ()=>{
      m.update({
        confirmLoading:true
      })
      await onOk()
      m.update({
        confirmLoading: false
      })
    }, onCancel,
  });
}

export const Modal = ({title, icon, content, confirm="confirm", cancel,})=>{
  AntdModal.confirm({
    title: <FormattedMessage id={title} defaultMessage="Confirm"/>,
    icon: icon || <ExclamationCircleOutlined />,
    content: <FormattedMessage id={content} defaultMessage=""/>,
    okText: <FormattedMessage id={confirm} defaultMessage=""/>,
    cancelText: <FormattedMessage id={cancel} defaultMessage=""/>,
    cancelButtonProps: { disabled: true }
  });
}

export const PromiseModal = ({title = 'confirm', content = 'confirm', confirm="confirm",}={}) => {
  const intl= getIntl();

  return new Promise((res,rej)=>{
    AntdModal.confirm({
      title: intl.formatMessage({id: title, defaultMessage: ''}),
      icon: <ExclamationCircleOutlined />,
      content: intl.formatMessage({id: content, defaultMessage: ''}),
      okText: intl.formatMessage({id: confirm, defaultMessage: ''}),
      onOk: ()=>{
        return new Promise((resolve, reject) => {
          resolve(true);
          res(true)
        }).catch(() => console.log('Oops errors!'));  
      },
      cancelButtonProps: { style: {display: 'none'} }
    });
  });
}

export const openNotification = ({title = 'confirm' , content= '', contentString, onClose=()=>{}, confirm='confirm'}) => {
  const intl= getIntl();

  let key = `open${Date.now()}`;
  const btn = (
    <Button type="primary" size="small" onClick={() => notification.close(key)}>
      {intl.formatMessage({id: confirm, defaultMessage: '  '})}
    </Button>
  );
  notification.error({
    message: title && intl.formatMessage({id: title, defaultMessage: '  '}),
    description: contentString || intl.formatMessage({id: content, defaultMessage: '  '}), btn, key, onClose,
  });
};