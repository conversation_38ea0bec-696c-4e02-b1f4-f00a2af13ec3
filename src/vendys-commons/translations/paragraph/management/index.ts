// eslint-disable-next-line import/prefer-default-export
export const MealPermissionDesc = `(!) 식권대장의 대장마켓플레이스 권한은 식권대장 > 기타 관리 > 관리자 권한 관리를 통해 설정 가능합니다.
(!) 다른 추가 권한을 부여하지 않은 경우, 일반 사용자로서, 식권대장앱 서비스만 이용가능합니다.
`;

export const SikdaeDisabledNotice = `
(!) 식권대장 이용을 위해선, 벤디스 담당자 혹은 고객센터(1644-5047)을 통해 먼저 상의해주시길 바랍니다.
`;

export const SikdaeActiveNotice = `
(!) 사용자는 식권대장앱에 로그인 및 식권대장 서비스 이용이 불가합니다.
(!) 단, 복지대장 서비스를 이용중인 경우 로그인 및 복지대장 서비스 이용이 가능합니다.
(!) 일시 중지 기간 동안에는, 식대가 자동 지급되지 않습니다.
(!) 일시 중지 전, 주문된 배송, 예약배달식사건은 취소되지 않습니다.
(!) 단, 대장마켓플레이스 로그인은 가능하며, 과거 기록 확인 및 기능은 사용할 수 있습니다.
`;

export const SikdaePauseCaution = `일시 정지 진행시, 아래와 같은 내용이 적용됩니다.
반드시 확인 후, 진행해주세요.


(!) 사용자는 식권대장앱에 로그인 및 식권대장 서비스 이용이 불가합니다.
(!) 단, 복지대장 서비스를 이용중인 경우 로그인 및 복지대장 서비스 이용이 가능합니다.
(!) 일시 중지 기간 동안에는, 식대가 자동 지급되지 않습니다.
(!) 일시 중지 전, 주문된 배송, 예약배달식사건은 취소되지 않습니다.
(!) 단, 대장마켓플레이스 로그인은 가능하며, 과거 기록 확인 및 기능은 사용할 수 있습니다.
`;

export const SikdaeStartCaution = `일시 정지 해제 진행시, 아래와 같은 내용이 적용됩니다.
반드시 확인 후, 진행해주세요.


(!) 설정된 식대는 해제 시점 이후로, 정해진 규칙대로 지급됩니다.
(!) 단, 일시 중지 기간동안 지급되지 않은 식대는 소급되어 지급되지 않습니다.
(!) 일시정지 해제 시점부터 사용자는 식권대장앱에서 결제를 할 수 있습니다.
`;

export const ServicePauseCaution = `일시 정지 진행시, 아래와 같은 내용이 적용됩니다.
반드시 확인 후, 진행해주세요.


(!) 새로운 예약 및 주문 등을 진행 할 수 없습니다.
(!) 일시 중지 전 예약 및 주문된 내용은 이미 진행되고 있는 상황으로 자동 취소되지 않습니다.
(!) 단, 해당 주문 및 예약의 상태가 취소 가능한 경우, 취소 진행 가능 합니다.
`;

export const ServiceStartCaution = `일시 정지 해제 진행시, 아래와 같은 내용이 적용됩니다.
반드시 확인 후, 진행해주세요.


(!) 일시 정지 해제 시점부터 사용자는 대장마켓플레이스 서비스에서 즉시 주문 및 예약을 할 수 있습니다.
`;

export const WelfarePauseCaution = `일시 중지 진행시, 아래와 같은 내용이 적용됩니다.
반드시 확인 후, 진행해주세요.


(i) 사용자는 식권대장앱에 로그인 및 복지대장 서비스 이용이 불가합니다.
(i) 단, 식권대장 서비스를 이용중인 경우 로그인 및 식권대장 서비스 이용이 가능합니다.
(i) 일시 중지 기간 동안에는, 예약중인 복지포인트가 지급/취소되지 않습니다.
(i) 일시 중지 전에 주문된 배송, 복지포인트 예약 지급/취소건은 취소되지 않습니다.
(i) 단, 대장마켓플레이스 로그인은 가능하며, 과거 기록 확인 및 기능은 사용할 수 있습니다.
`;

export const WelfareStartCaution = `일시 중지 해제 진행시, 아래와 같은 내용이 적용됩니다.
반드시 확인 후, 진행해주세요.


(!) 설정된 복지포인트는 해제 시점 이후로, 정해진 규칙대로 지급됩니다.
(!) 일시 중지 해제 시점부터 사용자는 식권대장앱 로그인 및 복지대장 서비스이용이 가능합니다.
`;

export const WelfareDisabledNotice = `
(i) 복지대장 서비스 안내 화면에서 서비스를 신청해주시면 담당자 확인 후 안내 도와드리겠습니다.
`;

export const WelfareActiveNotice = `
(i) 사용자는 식권대장앱에 로그인 및 복지대장 서비스 이용이 불가합니다.
(i) 단, 식권대장 서비스를 이용중인 경우 로그인 및 식권대장 서비스 이용이 가능합니다.
(i) 일시 중지 기간 동안에는, 예약중인 복지포인트가 지급/취소되지 않습니다.
(i) 일시 중지 전에 주문된 배송, 복지포인트 예약 지급/취소건은 취소되지 않습니다.
(i) 단, 대장마켓플레이스 로그인은 가능하며, 과거 기록 확인 및 기능은 사용할 수 있습니다.
`;

export const QuickDisabledNotice = `
(i) 서비스 이용 신청 후 즉시 사용 가능합니다.
(i) 이용 방법, 정산 방식등의 약관을 꼭 확인해 주시길 바랍니다.
`;

export const QuickActiveNotice = `
(i) 새로운 예약 및 주문 등을 진행 할 수 없습니다.
(i) 일시 중지 전 예약 및 주문된 내용은 이미 진행되고 있는 상황으로 자동 취소되지 않습니다.
(i) 단, 해당 주문 및 예약의 상태가 취소 가능한 경우, 취소 진행 가능 합니다.
`;
