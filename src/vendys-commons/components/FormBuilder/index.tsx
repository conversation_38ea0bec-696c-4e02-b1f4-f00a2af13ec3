import { Button,Switch, Checkbox, Space, Radio, Row, Form, Col, Input, InputNumber } from 'antd';
import React, { ReactElement, useState, useRef, useEffect, useMemo } from 'react'
import NumberFormat from 'react-number-format';

import EnumSelector from 'vcomponents/Form/EnumSelector'
import CheckboxGroup from 'vcomponents/Form/CheckboxGroup'
import { Content, MainContent, ActionContent } from './styles';
import { ConditionalWrapper } from 'utils/view'


import * as TextDeco from 'decorators/text';
import * as ViewDeco from 'decorators/views';

const Decorators = {...TextDeco, ...ViewDeco};

import callServer from 'apis/server'

import objC from 'utils/data/obj'
import { FormattedMessage } from 'react-intl';

var d = {
    a: 'a',
    b: 'b',
    c: {
        "c-1": 'v-1'
    }
}

export interface FormBuilderProps {
    template: []
    defaultValues: object
    onClick: (v)=> object;
    colInRow: number;
    layout: 'vertical' | 'horizontal'
}

const view = {

}
const layout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
};
const renderRow = (props)=>{
    var {id, label, type, data, list = [], name, meta={}, placeholder, layout, rules = [], required = false, defaultValue, onChange, defaultLabel} = props;
      let content = null;
  
      const inputEl = useRef(null);
      useEffect(()=>{
        if(!inputEl.current) return;
      },[inputEl]);

      switch(type){
          case 'enum':
              content = <EnumSelector id={id} data={data} onChange={onChange} defaultValue={defaultValue} defaultLabel={defaultLabel} {...meta} />;
          break;

          case 'radio':
            content = <Radio.Group ref={inputEl} id={id} onChange={onChange} defaultValue={defaultValue} >
                <ConditionalWrapper
                    condition={layout === 'vertical'}
                    wrapper={children => <Space direction="vertical">{children}</Space>}
                >
                {list.map(({value, label})=>{
                    return <Radio value={value}>{label}</Radio>
                })}
                </ConditionalWrapper>
            </Radio.Group>
          break;

          case 'checkbox':
            //   content = <Checkbox.Group ref={inputEl} id={id}  options={list} defaultValue={defaultValue} onChange={onChange} />
            content = <CheckboxGroup ref={inputEl} id={id}  options={list} defaultValue={defaultValue} onChange={onChange} {...meta} />
            break;
          case 'text':

            break;
          case 'string':
              content = <Input placeholder={placeholder} ref={inputEl} id={id} onChange={onChange} defaultValue={defaultValue} style={{}} {...meta}/>;
          break;

          case 'number':
              content = <InputNumber ref={inputEl} id={id} onChange={onChange} defaultValue={defaultValue} style={{}} />;
          break;

          case 'switch':
              content = <Switch ref={inputEl} id={id} onChange={onChange} defaultValue={defaultValue} style={{}} />;
          break;

          case 'email':
                content = <Input placeholder={placeholder} ref={inputEl} id={id} onChange={onChange} defaultValue={defaultValue} style={{}} {...meta}/>;
                rules.push({ type: 'email' })
          break;

          case 'phone':
                content = <NumberFormat ref={inputEl} id={id} onChange={onChange} format="###-####-####" mask="_"/>
          break;

          case 'crn':
            content = <NumberFormat placeholder={placeholder} ref={inputEl} id={id} onChange={onChange} format="###-##-#####" mask="_"/>
          break;
  
          case 'component':
              content = React.cloneElement(data, {onChange});
            break;
        
                default:
                    const View = Decorators[type]
                    content = <View ref={inputEl} id={id} onChange={onChange} {...meta} />

                break;
      }
  
      return <Form.Item key={label}
          name={name}
          label={<FormattedMessage id={label} defaultMessage={label} />}
          rules={rules}
      >
          {content}
      </Form.Item>
  }
  
const FormBuilder:React.FC<FormBuilderProps> = (props) =>{
    var { template = [], onClick = ()=>{} , colInRow = 3, defaultValues = {}, data} = props;
    var [ _data, setData ] = useState({...defaultValues});

    const _onClick = ()=>{
        onClick(_data);
    }

    const _onItemChange = (id,v)=>{
        if(v && v.target && v.target.value){
            v = v.target.value;
        }
        setData({..._data, [id]: v});
    }

    return <Content>
        <MainContent>
            <Form {...props} {...layout} style={{display: 'flex', flex: 1}}>
                <Row gutter={24} style={{marginRight: 0, flex:1 }}>
                    {
                        template.map((row,i)=>{
                            var { id, span } = row;
                            var dv = defaultValues[id];
                            var width = span ? Math.floor(24/colInRow)*span : Math.floor(24/colInRow)

                            return <Col span={width} key={i}>
                                {renderRow({...row , defaultValue: dv, onChange: (v)=>_onItemChange(id,v)})}
                            </Col>
                        })
                    }
                </Row>
            </Form>
        </MainContent>
    </Content>
}

export default FormBuilder;