import React, { ReactElement, FC } from 'react'

import { Button, Table, Tag, Space } from 'antd';

import { Container } from './styles'

interface AutoComponentProps {
    onClick: ()=>{}
    columns: []
    data: []
    project: string
}

const AutoComponent : FC<AutoComponentProps>  = function({columns, data, onClick, project}) {

    return <Container>
        <Button onClick={onClick} >조회</Button>
        <Table columns={columns} dataSource={data} />        
    </Container>
}

export default AutoComponent;