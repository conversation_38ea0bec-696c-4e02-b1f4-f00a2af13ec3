import { submit } from 'redux-form'
import { produce } from 'immer'
import { ActionFunction, AsyncActionFunction, ReducerFunction, ReducerObject } from 'common-interfaces/ReducerInterface';

const VIEW_NAME = "VIEW_NAME"

const INIT_VIEW = "whatap/"+VIEW_NAME+"/INIT_VIEW"

const LOAD_DATA = "whatap/"+VIEW_NAME+"/LOAD_DATA"
const LOAD_DATA_SUCCESS = "whatap/"+VIEW_NAME+"/LOAD_DATA_SUCCESS"
const LOAD_DATA_FAIL = "whatap/"+VIEW_NAME+"/LOAD_DATA_FAIL"

const SHOW_MODAL = "whatap/MODAL/SHOW_MODAL"
const HIDE_MODAL = "whatap/MODAL/HIDE_MODAL"

export function initModalView(id: string): ActionFunction {
    return {
        type: '$whatap/'+id+'/INIT_VIEW',
        payload: {}
    }
}

export function destroyModalView(id: string): ActionFunction {
    return {
        type: '$whatap/'+id+'/DESTROY_VIEW',
        payload: {}
    }
}

export function loadData(): AsyncActionFunction {
    return {
        types: [LOAD_DATA, LOAD_DATA_SUCCESS, LOAD_DATA_FAIL],
        promise: (client: any) => {
            return client.get('/url');
        }
    }
}

export function triggerSubmit(id: string){
    return submit(id);
}

export function showModalWithData(id: string, modalData: any): ActionFunction {
    return {
        type: SHOW_MODAL,
        payload: { id, modalData }
    }
}
export function showModal(id: string, params: any): ActionFunction {
    return {
        type: SHOW_MODAL,
        payload: { id, params }
    }
}

export function hideModal(id: string): ActionFunction {
    return {
        type: HIDE_MODAL,
        payload: { id },
    }
}

const ACTION_HANDLERS: ReducerObject = {
    [INIT_VIEW]: (state, { payload }) =>{
      return produce<any>(state, draft => {})
    },
    [SHOW_MODAL]: (state, { payload }) => {
      return produce<any>(state, draft => {
        const { id, modalData } = payload;
    
        draft[id] = { ...draft[id], visible: true, modalData };
      })
    },
    [HIDE_MODAL]: (state, { payload }) => {
      return produce<any>(state, draft => {
        const { id } = payload;
    
        draft[id] = { ...draft[id], visible: false };
      })
    },
    [LOAD_DATA_SUCCESS]: (state, action) => {
      return produce<any>(state, draft => {})
    }
}

interface StateInterface {
  readonly [key: string]: any;
}

const initialState: StateInterface = {
}    


export default function Actions(state: any = initialState, action: ActionFunction): ReducerFunction {
  const handler = ACTION_HANDLERS[action.type];
  return handler ? handler(state, action) : state
}