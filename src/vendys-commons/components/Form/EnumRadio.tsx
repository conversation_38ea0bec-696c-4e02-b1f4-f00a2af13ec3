import * as React from 'react';
import { Select, Button, Radio } from 'antd';
import { useState, useEffect, useMemo } from 'react';
const Option = Select.Option;
const ButtonGroup = Button.Group;
import * as EnumData from 'constants/enum'

export interface IEnumRadioProps {
  buttonGroup: boolean;
  data: object;
  value: string;
  enumId?: string;
  defaultValue?: string;
  onChange?: (String) => void;
  style: object;
  disables?: object;
  disabledAll?: boolean;
}

const EnumRadio:React.FC<IEnumRadioProps> = (props)=>{
  var { data, value, buttonGroup = true, enumId, defaultValue, onChange = ()=>{}, style, disables = [], disabledAll = false } = props;

  var [ _value, setValue ] = useState(undefined)

  const _data = useMemo(()=>{
    if(data) return data;
    if( EnumData && EnumData[enumId]) return EnumData[enumId];
    },[data, enumId])


  useEffect(()=>{
    var isExist = false;
    Object.keys(_data).map((key,idx)=>{
      if(key == _value){
        isExist = true
      }
    })

    if(isExist) return;

    Object.keys(_data).map((key,idx)=>{
      if(idx == 0){
        setValue(key)
      }
    })
  }, [_data])

  useEffect(()=>{
    if( value == _value) return;
    setValue(value);
  }, [value])

  const _onChange = (v)=>{
    setValue(v);
    onChange(v);
  }

  if(!_data){
      return <div></div>
  }

    return (<div style={{...style}}>
      <Radio.Group defaultValue={_value || defaultValue} value={_value} onChange={e=>_onChange(e.target.value)} disabled={disabledAll}>
      {_data && Object.keys(_data).map(key=>{
        var v = _data[key];
        const disabled = disables && disables[key] || false;
        return <Radio key={key} value={key} disabled={disabled}>{v}</Radio>
      }
      ) }
      </Radio.Group>
  </div>);

}

export default EnumRadio;
