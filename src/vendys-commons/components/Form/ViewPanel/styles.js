import styled from 'styled-components';
// import { propsToStyle } from 'utils'
import { Space } from 'antd';

export const Container = styled.div`
  display: flex;
  padding: 12px 16px;
  flex-direction: column;
  border-radius: ${(props) => props.theme.radius}rem;

  background-color: ${(props) => props.theme.componentBackground};
  color: #495057;

  &.default {
    box-shadow: 0 0.46875rem 2.1875rem rgba(8, 10, 37, 0.03), 0 0.9375rem 1.40625rem rgba(8, 10, 37, 0.03),
      0 0.25rem 0.53125rem rgba(8, 10, 37, 0.05), 0 0.125rem 0.1875rem rgba(8, 10, 37, 0.03);
  }

  &.none {
  }

  &.line {
    border: 1px solid rgba(72, 94, 144, 0.16);
  }
`;

export const PanelC = styled.div.attrs({
  className: 'panelC'
})`
  padding: 0px !important;
  position: relative;
  width: 100%;
  height: 100%;
  background: #ffffff;
  border-radius: 0px;
  border: solid 1px #cad2e1;
`;

export const PanelInner = styled.div.attrs({
  className: ''
})`
  ${(props) =>
    props.headerVisible === true
      ? `
    width: 100%;
    height: 100%;
    top: 0;
    position: absolute;
    padding: 35px 3px 2px;
  `
      : `
    width: 100%;
    height: 100%;
    top: 0;
    position: absolute;
    padding:  13px 0px 13px 10px;
  `};
`;

export const PanelHeader = styled.div.attrs({
  className: 'panel-header'
})`
  position: relative;
  padding: 0px 6px;
  font-size: 13px;


  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
  height: 28px;
  line-height: 28px;

  /*${(props) => (props.style ? propsToStyle(props.style) : '')};*/
`;
export const PanelHeaderInner = styled.div.attrs({
  className: 'panel-header-inner'
})`
  float: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  ${(props) =>
    props.isCubePanel === true
      ? `
    max-width: 60%;
  `
      : `
    max-width: calc(80% - 65px);
  `};
`;

export const PanelTitleBar = styled.div.attrs({
  className: 'panel-titlebar'
})`
  ${(props) =>
    props.mobileLayout &&
    `
    & .filler {
      fill: #d9d9d9;
    }
    `}
  ${(props) =>
    props.countView
      ? `
    display: inline-block;
    padding-right: 8px;
    border-right: 1px solid #d8d8d8;
    `
      : `
    display: inline;
    // padding-right: 5px;
  `};
`;

export const HeaderRight = styled.div.attrs({
  className: 'panel-header-right'
})`
  right: 1em;
  float: right;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
`;

// export const SubTitle = styled.div.attrs({
//   className: 'panel-subtitle'
// })`
//   display: inline-block;
// `

export const CountAndMeter = styled.div.attrs({
  className: 'panel-titlebar'
})`
  ${(props) =>
    props.countView && props.isDashboard
      ? `
    font-size: 16px;
    font-weight: bold;
    margin-left: 8px;
    display: inline;
    color:  #4496EC;
    `
      : `

  `};
  ${(props) =>
    props.errCountView
      ? `
    font-size: 16px;
    font-weight: bold;
    margin-left: 8px;
    display: inline;
    color:  #EEAA46;
    `
      : `

  `};
`;

export const CustomButtonStyles = styled.div`
  display: inline-block;
`;

export const Divide = styled.div`
  display: inline;
  height: 12px;
  border-right: ${(props) =>
    props.theme === 'bk' ? 'solid 1px rgba(217, 226, 235, 0.4)' : 'solid 1px rgb(217, 226, 235)'};
  margin: 0px 3px;
`;

export const CloseBtn = styled.div`
  vertical-align: super;
  display: inline-block;
`;

export const DetailBtn = styled.div.attrs({ className: 'detail-btn' })`
  display: inline-block;
  position: relative;
  z-index: 1;
  padding-bottom: 8px;
`;

export const Header = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: ${(props) => props.theme.verticalMargin};
`;

export const Body = styled.div`
  // height: 100%;
  display: flex;
  flex: 1;
  flex-basis: auto;
  flex-direction: ${(props) => (props.layout == 'vertical' ? 'column' : 'row')};
  align-items: strech;
  // justify-content: center;
  position: relative;
`;

export const LeftHeader = styled.div`
  flex: 1;
`;

export const ActionButtons = styled(Space)``;

export const Title = styled.div`
  font-size: ${(props) => props.theme.fontSize1};
  font-weight: 700;
  margin-bottom: 8px;
`;

export const SubTitle = styled.div`
  font-size: 0.88rem;
  font-weight: 400;
  opacity: 0.5;
`;

export const Count = styled.div`
  font-size: 1.8rem;
  font-weight: 700;
  color: #3ac47d;
`;

export const Description = styled.div`
  background-color: #f3f6f9;
  border-color: #f3f6f9;
  padding: 1.5rem 2rem;
  border: 1px solid transparent;
  border-radius: 0.42rem;

  margin-bottom: ${(props) => props.theme.verticalMargin};
`;
