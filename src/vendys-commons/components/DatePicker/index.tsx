import React, { ReactElement, useState, useRef } from 'react'
import Picker from 'react-month-picker'
import PropTypes from 'prop-types'
import moment from 'moment'
import { Select } from 'antd';
const { Option } = Select;
import 'react-dates/initialize';
import { DateRangePicker, SingleDatePicker, DayPickerRangeController } from 'react-dates';

import 'react-month-picker/css/month-picker.css'
import 'react-dates/lib/css/_datepicker.css';
import { Container } from './styles';


interface Props {

}
class MonthBox extends React.Component {
  static propTypes = {
      value: PropTypes.string,
      onClick: PropTypes.func,
  }

  constructor(props, context) {
      super(props, context)

      this.state = {
          value: this.props.value || 'N/A',
      }
  }

  static getDerivedStateFromProps(props, state) {
      return {
          value: props.value || 'N/A',
      }
  }

  render() {

      return (
          <div className="box" onClick={this._handleClick}>
              <label>{this.state.value}</label>
          </div>
      )
  }

  _handleClick = (e) => {
      this.props.onClick && this.props.onClick(e)
  }
}

const pickerLang = {
  months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
  from: 'From', to: 'To',
}

const momentToYM = (m)=>{
  return {year: parseInt( m.year() ), month: parseInt( m.month() )+1}
}

export default function DatePicker({ onChange= ()=>{} }: Props): ReactElement {
//   const [ value, setValue ]= useState({from: {year: parseInt(moment().format('YYYY')), month: parseInt(moment().format('MM'))+1},
// to:  {year: parseInt(moment().format('YYYY')), month: parseInt(moment().format('MM'))+1} });
  const [ value, setValue ] = useState({from:moment(), to:moment()})
  const [ focusedInput, setFocusedInput] = useState('START_DATE');

  const [ type, setType] = useState("D");
  const pickMonth = useRef(undefined)

  const _onChange = (year,month,index)=>{
    var newM = undefined;
    if(index == 0){
      newM = value.from.clone();
      newM.year(year); newM.month(month);
      setValue({from: newM, to: value.to });
    }else{
      newM = value.from.clone();
      newM.year(year); newM.month(month);
      setValue({from: value.from, to: newM });
    }
  }

  const handleClickMonthBox = (e) => {
    pickMonth.current.show()
  }
  const makeText = m => {
    if (m && m.year && m.month) return (pickerLang.months[m.month-1] + '. ' + m.year)
    return '?'
  }

  const onDismiss = ()=>{
    var startDate = value.from;
    var endDate = value.to;
    onChange({startDate,endDate})
  }

  return (
    <Container>
      <Select defaultValue={type} onChange={setType}>
        <Option value="D">D</Option>
        <Option value="M">M</Option>
      </Select>
      <div style={{height: '3.3rem'}}>
      {type == 'M' && <Picker
        ref={pickMonth} onChange={_onChange} onDismiss={onDismiss} lang={pickerLang.months}
        years={{min: {year: 2016, month: 1}, max: {year: moment().format('YYYY'), month: parseInt(moment().format('MM'))+1}}}
        value={{from: momentToYM(value.from), to: momentToYM(value.to)}} >
          <MonthBox value={makeText(momentToYM(value.from)) + ' ~ ' + makeText(momentToYM(value.to))} onClick={handleClickMonthBox}  />
        </Picker>}

      {type == "D" &&
      <DateRangePicker
        startDate={value.from} endDate={value.to} startDateId="startDate" endDateId="endDate"
        onDatesChange={({ startDate:from, endDate:to })=> {setValue({from,to})} }
        focusedInput={focusedInput} onFocusChange={setFocusedInput} onClose={onDismiss}
      />
      }
      </div>
    </Container>
  )
}
