import styled, { css } from 'styled-components'
export const TabBarContainer = styled.div`
    & .ant-tabs-nav .ant-tabs-tab {
      height: auto;
      margin-bottom: unset;
    }
    & .ant-tabs-bar, .ant-tabs-nav-container, .ant-tabs-nav-wrap {
      height: auto !important;
    }
    & .ant-tabs-content {
      padding-left: 10px !important;
      padding-right: 10px  !important;
    }
`

export const GroupComponent = styled.div`
  display: inline-block;
  margin-right: 20px;
  margin-bottom: 0px;

    & .setion-heading {
      margin-bottom: 8px;
      font-size: 14px;
      font-weight: 400;
    }
`


export const GroupTitle = styled.div`
    font-size: 13px;
    font-weight: bold;
`

export const TemplateBuilderDiv = styled.span`
    & .ant-form-item {
        margin-bottom: 0px;
    }

    & .ant-tabs-nav .ant-tabs-tab {
        height: auto;
        margin-bottom: unset;
      }
      & .ant-tabs-bar, .ant-tabs-nav-container, .ant-tabs-nav-wrap {
        height: auto !important;
      }
      & .ant-tabs-content {
        padding-left: 10px !important;
        padding-right: 10px  !important;
      }
    & .ant-form-item-label{
      text-align: left;
    }

    & .ant-form-item-label > label{
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      vertical-align: middle;
      // padding-left: 4px;
      padding-right: 6px;
    }
`


