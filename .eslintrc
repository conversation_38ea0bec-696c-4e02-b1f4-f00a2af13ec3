{
  "env": {
    "browser": true,
    "node": true,
    "es6": true
  },
  "extends": [
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:jsx-a11y/recommended",
    "plugin:import/recommended",
    "airbnb",
    "prettier"
  ],
  "settings": {
    "import/resolver": {
      "node": {
        "paths": ["src", "public", "config", "src/config"],
        "extentions": [".js", ".jsx", ".ts", ".tsx"]
      }
    },
    "import/extensions": [".js", ".mjs", ".jsx", ".js", ".jsx", ".ts", ".tsx"]
  },
  "globals": {
    "Atomics": "readonly",
    "SharedArrayBuffer": "readonly",
    "$": true
  },
  "parser": "babel-eslint",

  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": 2018,
    "sourceType": "module"
  },
  "plugins": ["react", "jsx-a11y", "import", "prettier", "@typescript-eslint"],
  "rules": {
    "prettier/prettier": "error",
    "no-console": 0,
    "comma-dangle": 0,
    "react/jsx-filename-extension": 0,
    "react/jsx-one-expression-per-line": 0,
    "react/no-deprecated": 0,
    "object-curly-newline": [
      "error",
      {
        "ImportDeclaration": { "multiline": true }
      }
    ],
    "react/jsx-no-bind": [
      2,
      {
        "ignoreRefs": false,
        "allowArrowFunctions": true,
        "allowBind": false
      }
    ],
    "react/prop-types": 0,
    "react/jsx-fragments": 0,
    "no-plusplus": [2, { "allowForLoopAfterthoughts": true }],
    "react/jsx-props-no-spreading": [
      "off",
      {
        "custom": "ignore"
      }
    ],
    "import/extensions": [
      "error",
      "ignorePackages",
      {
        "js": "never",
        "jsx": "never",
        "ts": "never",
        "tsx": "never"
      }
    ],
    "no-underscore-dangle": "off"
  },
  "overrides": [
    {
      "files": ["**/*.ts", "**/*.tsx"],
      "extends": [
        "plugin:import/errors",
        "plugin:import/typescript",
        "plugin:prettier/recommended",
        "plugin:@typescript-eslint/eslint-recommended",
        "prettier/@typescript-eslint"
      ],
      "parser": "@typescript-eslint/parser",
      "parserOptions": {
        "project": "./tsconfig.json"
      },
      "plugins": ["prettier", "@typescript-eslint"],
      "rules": {
        "no-use-before-define": "off",
        "react/require-default-props": "off"
        //        "indent": ["error", 4],
        //        "prettier/prettier": ["error", { "tabWidth": 4 }]
      },
      "settings": {
        "import/parsers": {
          "@typescript-eslint/parser": [".ts", ".tsx"]
        },
        //        "import/resolver": {
        //          "typescript": "./tsconfig.json",
        //          "node": {
        //            "paths": ["src", "public", "config", "src/config"],
        //            "extentions": [".js", ".jsx", ".ts", ".tsx"]
        //          }
        //        }
        "import/resolver": {
          "typescript": {}
        }
      }
    }
  ]
}
