<!DOCTYPE html>
<html lang="ko">
  <head>
    <meta charset="utf-8" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/semantic-ui/dist/semantic.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bluebird/3.3.4/bluebird.min.js"></script>

    <script
      type="text/javascript"
      src="https://openapi.map.naver.com/openapi/v3/maps.js?ncpClientId=ec8jxdwg05&submodules=geocoder"
    ></script>
    <script src="https://code.jquery.com/jquery-3.2.1.min.js"></script>
    <script src="https://t1.daumcdn.net/mapjsapi/bundle/postcode/prod/postcode.v2.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@jungsoo/excel-export@1.3.1/dist/excel.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.5.2/animate.min.css" />
    <title>대장마켓플레이스</title>
    <style>
      .fade-enter {
        opacity: 0;
        transform: scale(1.1);
        position: absolute;
        inset: 0px;
      }

      .fade-enter-done {
      }

      .fade-enter-active {
        opacity: 1;
        transform: scale(1);
        transition: opacity 700ms, transform 700ms;
      }

      .fade-exit {
        opacity: 1;
        transform: scale(1);
        position: absolute;
        inset: 0px;
      }

      .fade-exit-done {
      }

      .fade-exit-active {
        opacity: 0;
        transform: scale(0.9);
        transition: opacity 700ms, transform 700ms;
      }

      /* .fade-enter {
        opacity: 0;
        z-index: 1;
      }

      .fade-enter.fade-enter-active {
        opacity: 1;
        transition: opacity 500ms ease-in;
      } */
    </style>
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
